# 宠物管理服务 (Manager Service)

基于Midway.js框架开发的宠物管理系统后端服务。

## 功能特性

- 用户管理与认证
- 宠物信息管理
- 会员卡管理
- 优惠券系统
- 产品与服务管理
- 活动管理
- 区域管理
- 员工管理与考勤
- 权限控制

## 快速开始

### 开发环境

```bash
# 安装依赖
$ npm i

# 启动开发服务器
$ npm run dev

# 访问服务
$ open http://localhost:7001/
```

### 代码检查与测试

```bash
# 代码风格检查
$ npm run lint

# 运行单元测试
$ npm test
```

## 部署说明

### 方法一：使用部署脚本

项目提供了自动化部署脚本，可以快速部署到不同环境：

```bash
# 使用方法
$ ./scripts/deploy.sh <环境>

# 可用环境参数
# bc - 贝宠环境
# zpl - 线上测试环境
# hw - 线上测试环境
```

部署脚本会自动执行以下操作：
1. 安装依赖
2. 构建项目
3. 移除开发依赖
4. 使用PM2启动或重启服务

### 方法二：手动部署

```bash
# 安装依赖并构建
$ npm install
$ npm run build

# 启动生产服务
$ npm start
```

## 技术栈

- [Midway.js](https://midwayjs.org) - 基于TypeScript的Node.js框架
- TypeORM - 数据库ORM框架
- PM2 - Node.js进程管理工具

## 相关文档

- [Midway官方文档](https://midwayjs.org)
