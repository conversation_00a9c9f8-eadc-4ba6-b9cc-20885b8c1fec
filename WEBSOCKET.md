# WebSocket 服务

## 简介

基于 Midway.js 实现的简单高性能 WebSocket 服务，专注于文本消息的实时通信。

## 实现文件

### 核心文件（仅2个）

1. **WebSocket 控制器** - `src/socket/websocket.controller.ts`
   - 处理连接、消息、断开连接事件
   - 支持文本消息和心跳检测
   - 自动广播消息给所有连接的客户端

2. **WebSocket 服务** - `src/service/websocket.service.ts`
   - 提供消息广播功能
   - 客户端连接数统计

### 配置修改

- `src/configuration.ts` - 添加了 `@midwayjs/ws` 组件
- `src/config/config.default.ts` - 添加了 WebSocket 配置
- `package.json` - 添加了 `@midwayjs/ws` 依赖

## 功能特性

- ✅ WebSocket 连接管理
- ✅ 文本消息实时广播
- ✅ 心跳检测
- ✅ 内存存储（高性能）
- ✅ 简单易用

## 使用方式

### 启动服务
```bash
npm run dev
```

### 连接 WebSocket
```javascript
const ws = new WebSocket('ws://localhost:3001');
```

### 发送消息
```javascript
// 文本消息
ws.send(JSON.stringify({
  type: 'chat',
  content: 'Hello World!'
}));

// 心跳消息
ws.send(JSON.stringify({
  type: 'ping'
}));
```

### 接收消息
```javascript
ws.onmessage = function(event) {
  const data = JSON.parse(event.data);
  console.log('收到消息:', data);
};
```

## 消息格式

### 连接成功消息
```json
{
  "type": "connected",
  "clientId": "client_1234567890_abc123",
  "message": "连接成功",
  "timestamp": 1640995200000
}
```

### 聊天消息
```json
{
  "type": "message",
  "clientId": "client_1234567890_abc123",
  "content": "Hello World!",
  "timestamp": 1640995200000
}
```

### 心跳响应
```json
{
  "type": "pong",
  "timestamp": 1640995200000
}
```

## 特点

- **极简设计**：只有2个核心文件
- **高性能**：内存存储，无数据库依赖
- **实时广播**：所有消息自动广播给所有客户端
- **文本专用**：专注于文本消息，无文件传输等复杂功能
- **易于扩展**：基于 Midway.js 框架，易于集成和扩展
