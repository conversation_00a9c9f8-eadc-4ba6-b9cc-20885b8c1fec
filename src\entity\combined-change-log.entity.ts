import {
  Table,
  Column,
  Model,
  DataType,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { CustomerCoupon } from './customer-coupon.entity';
import { Coupon } from './coupon.entity';
import { Customer } from './customer.entity';
import { Order } from './order.entity';
import { CustomerMembershipCard } from './customer-membership-card.entity';
import { MembershipCardType } from './membership-card-type.entity';

/**
 * 合并变更类型枚举
 */
export enum CombinedChangeType {
  // 代金券变更类型
  COUPON_PURCHASE = 'coupon_purchase',
  COUPON_GRANT = 'coupon_grant',
  COUPON_USE = 'coupon_use',
  COUPON_REFUND = 'coupon_refund',
  COUPON_ADMIN_GRANT = 'coupon_admin_grant',
  COUPON_ADMIN_DISABLE = 'coupon_admin_disable',
  COUPON_ADMIN_REVOKE = 'coupon_admin_revoke',
  COUPON_EXPIRE = 'coupon_expire',
  COUPON_ADD_TIMES = 'coupon_add_times',
  COUPON_DISABLE = 'coupon_disable',
  COUPON_REVOKE = 'coupon_revoke',
  // 权益卡变更类型
  MEMBERSHIP_PURCHASE = 'membership_purchase',
  MEMBERSHIP_USE = 'membership_use',
  MEMBERSHIP_REFUND = 'membership_refund',
  MEMBERSHIP_ADMIN_GRANT = 'membership_admin_grant',
  MEMBERSHIP_ADMIN_DISABLE = 'membership_admin_disable',
  MEMBERSHIP_ADMIN_REVOKE = 'membership_admin_revoke',
  MEMBERSHIP_EXPIRE = 'membership_expire',
  MEMBERSHIP_ADD_TIMES = 'membership_add_times',
  MEMBERSHIP_DISABLE = 'membership_disable',
  MEMBERSHIP_REVOKE = 'membership_revoke',
}

/**
 * 合并变更记录属性接口
 */
export interface CombinedChangeLogAttributes {
  /** 变更记录ID */
  id: number;
  /** 关联的用户代金券ID */
  userCouponId?: number;
  /** 关联的用户权益卡ID */
  cardId?: number;
  /** 关联的用户ID */
  customerId: number;
  /** 关联的代金券ID */
  couponId?: number;
  /** 关联的权益卡类型ID */
  cardTypeId?: number;
  /** 变更类型 */
  changeType: CombinedChangeType;
  /** 变更前状态 */
  beforeStatus?: string;
  /** 变更后状态 */
  afterStatus?: string;
  /** 关联的订单ID（使用时记录） */
  orderId?: number;
  /** 变更前剩余次数 */
  beforeRemainTimes?: string;
  /** 变更后剩余次数 */
  afterRemainTimes?: string;
  /** 变更描述 */
  description?: string;
  /** 操作人ID（后台操作时记录） */
  operatorId?: number;
  /** 用户名称（冗余存储） */
  customerName?: string;
  /** 用户手机号（冗余存储） */
  customerPhone?: string;
  /** 代金券名称（冗余存储） */
  couponName?: string;
  /** 代金券面值（冗余存储） */
  couponAmount?: number;
  /** 权益卡类型名称（冗余存储） */
  cardTypeName?: string;
  /** 权益卡售价（冗余存储） */
  cardTypePrice?: number;
  /** 订单编号（冗余存储） */
  orderNo?: string;
  /** 关联的用户代金券 */
  userCoupon?: CustomerCoupon;
  /** 关联的用户 */
  customer?: Customer;
  /** 关联的代金券 */
  coupon?: Coupon;
  /** 关联的订单 */
  order?: Order;
  /** 关联的用户权益卡 */
  card?: CustomerMembershipCard;
  /** 关联的权益卡类型 */
  cardType?: MembershipCardType;
}

/**
 * 卡券变更记录表
 */
@Table({
  tableName: 'combined_change_logs',
  timestamps: true,
  comment: '卡券变更记录表',
})
export class CombinedChangeLog
  extends Model<CombinedChangeLogAttributes>
  implements CombinedChangeLogAttributes
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '变更记录ID',
  })
  id: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true, // 允许为空，以便在关联记录被删除时不影响日志
    comment: '关联的用户代金券ID',
  })
  @ForeignKey(() => CustomerCoupon)
  userCouponId?: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true, // 允许为空，以便在关联记录被删除时不影响日志
    comment: '关联的用户权益卡ID',
  })
  @ForeignKey(() => CustomerMembershipCard)
  cardId?: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true, // 允许为空，以便在关联记录被删除时不影响日志
    comment: '关联的用户ID',
  })
  @ForeignKey(() => Customer)
  customerId: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true, // 允许为空，以便在关联记录被删除时不影响日志
    comment: '关联的代金券ID',
  })
  @ForeignKey(() => Coupon)
  couponId?: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true, // 允许为空，以便在关联记录被删除时不影响日志
    comment: '关联的权益卡类型ID',
  })
  @ForeignKey(() => MembershipCardType)
  cardTypeId?: number;

  @Column({
    type: DataType.STRING,
    comment: '用户名称（冗余存储）',
  })
  customerName?: string;

  @Column({
    type: DataType.STRING,
    comment: '用户手机号（冗余存储）',
  })
  customerPhone?: string;

  @Column({
    type: DataType.STRING,
    comment: '代金券名称（冗余存储）',
  })
  couponName?: string;

  @Column({
    type: DataType.DECIMAL(8, 2),
    comment: '代金券面值（冗余存储）',
  })
  couponAmount?: number;

  @Column({
    type: DataType.STRING,
    comment: '权益卡类型名称（冗余存储）',
  })
  cardTypeName?: string;

  @Column({
    type: DataType.DECIMAL(8, 2),
    comment: '权益卡售价（冗余存储）',
  })
  cardTypePrice?: number;

  @Column({
    type: DataType.ENUM(...Object.values(CombinedChangeType)),
    allowNull: false,
    comment: '变更类型',
  })
  changeType: CombinedChangeType;

  @Column({
    type: DataType.STRING,
    comment: '变更前状态',
  })
  beforeStatus?: string;

  @Column({
    type: DataType.STRING,
    comment: '变更后状态',
  })
  afterStatus?: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: true, // 允许为空，以便在关联记录被删除时不影响日志
    comment: '关联的订单ID（使用时记录）',
  })
  @ForeignKey(() => Order)
  orderId?: number;

  @Column({
    type: DataType.STRING,
    comment: '订单编号（冗余存储）',
  })
  orderNo?: string;

  @Column({
    type: DataType.STRING,
    comment: '变更前剩余次数',
  })
  beforeRemainTimes?: string;

  @Column({
    type: DataType.STRING,
    comment: '变更后剩余次数',
  })
  afterRemainTimes?: string;

  @Column({
    type: DataType.STRING,
    comment: '变更描述',
  })
  description?: string;

  @Column({
    type: DataType.INTEGER,
    comment: '操作人ID（后台操作时记录）',
  })
  operatorId?: number;

  @BelongsTo(() => CustomerCoupon, {
    onDelete: 'SET NULL', // 当用户代金券被删除时，将外键设为NULL
  })
  userCoupon?: CustomerCoupon;

  @BelongsTo(() => Customer, {
    onDelete: 'SET NULL', // 当用户被删除时，将外键设为NULL
  })
  customer?: Customer;

  @BelongsTo(() => Coupon, {
    onDelete: 'SET NULL', // 当代金券被删除时，将外键设为NULL
  })
  coupon?: Coupon;

  @BelongsTo(() => Order, {
    onDelete: 'SET NULL', // 当订单被删除时，将外键设为NULL
  })
  order?: Order;

  @BelongsTo(() => CustomerMembershipCard, {
    onDelete: 'SET NULL', // 当用户权益卡被删除时，将外键设为NULL
  })
  card?: CustomerMembershipCard;

  @BelongsTo(() => MembershipCardType, {
    onDelete: 'SET NULL', // 当权益卡类型被删除时，将外键设为NULL
  })
  cardType?: MembershipCardType;
}
