/*
 * @Description: 鉴权错误过滤器
 * @Date: 2025-01-09 10:56:08
 * @LastEditors: 朱鹏亮 <EMAIL>
 * @LastEditTime: 2025-01-09 10:56:19
 */
import { Catch, MidwayHttpError, httpError } from '@midwayjs/core';
import { Context } from '@midwayjs/koa';

@Catch(httpError.UnauthorizedError)
export class NoAuthFilter {
  async catch(err: MidwayHttpError, ctx: Context) {
    // 401 错误会到这里
    ctx.logger.error(err);
    throw new httpError.UnauthorizedError(err.message);
  }
}
