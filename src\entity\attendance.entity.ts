import {
  Table,
  Column,
  Model,
  DataType,
  BelongsTo,
  ForeignKey,
} from 'sequelize-typescript';
import { Employee, Vehicle } from '.';

export interface AttendanceAttributes {
  /** 考勤ID */
  id: number;
  /** 关联员工ID */
  employeeId: number;
  /** 关联车辆ID */
  vehicleId: number;
  /** 打卡照片链接 */
  checkInPhoto: string;
  /** 打卡时间 */
  checkInTime: Date;
  /** 关联的员工信息 */
  employee?: Employee;
  /** 关联的车辆信息 */
  vehicle?: Vehicle;
}

@Table({ tableName: 'attendances', timestamps: true, comment: '考勤表' })
export class Attendance
  extends Model<AttendanceAttributes>
  implements AttendanceAttributes
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '考勤ID',
  })
  id: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '关联员工ID',
  })
  @ForeignKey(() => Employee)
  employeeId: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '关联车辆ID',
  })
  @ForeignKey(() => Vehicle)
  vehicleId: number;

  @Column({
    type: DataType.STRING(255),
    allowNull: false,
    comment: '打卡照片链接',
  })
  checkInPhoto: string;

  @Column({
    type: DataType.DATE,
    allowNull: false,
    comment: '打卡时间',
  })
  checkInTime: Date;

  @BelongsTo(() => Employee)
  employee: Employee;

  @BelongsTo(() => Vehicle)
  vehicle: Vehicle;
}
