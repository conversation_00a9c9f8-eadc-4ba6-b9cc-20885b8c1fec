/*
 * @Description: 微信支付
 * @Version: 2.0
 * @Author: pengliang.zhu
 * @Date: 2025-04-16 20:53:26
 * @Last Modified by: pengliang.zhu
 * @Last Modified time: 2025-04-17 00:18:50
 */
import { Config, Provide } from '@midwayjs/core';
import * as crypto from 'crypto';
import path = require('path');

export type SignParams = {
  method: 'GET' | 'POST';
  url: string;
  query?: Record<string, any>;
  body?: Record<string, any>;
};

@Provide()
export class WePay {
  @Config('wePay')
  wePayConfig: {
    sp_mchid: string;
    sp_appid: string;
    pem_sn: string;
    sub_mchid: string;
    sub_appid: string;
  };

  // 证书私钥
  sym_key = require('fs').readFileSync(
    path.resolve(__dirname, '../cert/apiclient_key.pem'),
    'utf8'
  );

  /**
   * 生成签名
   * @param {object} params 请求参数
   * @param {string} params.url 绝对URL（请注意需要去除域名部分）
   * @param {object} [params.query] 查询参数
   * @param {object} [params.body] 请求体参数
   * @returns {string} 签名字符串
   */
  genSign({ method, url, query, body }: SignParams): string {
    let urlStr = url;
    if (query) {
      const queryStr = Object.keys(query)
        .map(key => {
          const value = query[key];
          if (typeof value === 'object') {
            return `${key}=${encodeURIComponent(JSON.stringify(value))}`;
          }
          return `${key}=${value}`;
        })
        .join('&');
      urlStr += `?${queryStr}`;
    }
    const timestamp = Math.floor(Date.now() / 1000);
    const nonce_str = crypto.randomBytes(16).toString('hex');
    const bodyStr = body ? JSON.stringify(body) : '';
    const message = `${method}\n${urlStr}\n${timestamp}\n${nonce_str}\n${bodyStr}\n`;
    // 使用SHA256 with RSA算法进行签名，并进行Base64编码
    const signature = crypto
      .createSign('RSA-SHA256')
      .update(message)
      .sign(this.sym_key, 'base64');
    return `WECHATPAY2-SHA256-RSA2048 mchid="${this.wePayConfig.sp_mchid}",nonce_str="${nonce_str}",signature="${signature}",timestamp="${timestamp}",serial_no="${this.wePayConfig.pem_sn}"`;
  }
}
