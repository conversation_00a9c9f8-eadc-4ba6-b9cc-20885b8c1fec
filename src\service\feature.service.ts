import { Provide } from '@midwayjs/core';
import { Feature } from '../entity/feature.entity';
import { BaseService } from '../common/BaseService';
import { Permission, Role } from '../entity';

@Provide()
export class FeatureService extends BaseService<Feature> {
  constructor() {
    super('功能');
  }

  getModel() {
    return Feature;
  }

  /**
   * 根据角色获取功能列表
   *
   * @param {number[]} roleIds 角色id
   * @return {Feature[]} 功能列表
   * @memberof FeatureService
   */
  async getListByRoles(roleIds: number[]) {
    if (!roleIds.length) {
      return [];
    }
    const permission = await Permission.findAll({
      include: [
        {
          model: Role,
          where: {
            id: roleIds,
          },
          required: true,
        },
      ],
    });
    const features = await Feature.findAll({
      include: [Permission],
    });

    // 返回未配置权限点或权限列表符合条件的功能列表
    return features
      .filter(
        f =>
          !f.permissions.length ||
          f.permissions.some(p => permission.map(p => p.id).includes(p.id))
      )
      .map(f => ({
        ...f.toJSON(),
        permissions: f.permissions.filter(p =>
          permission.map(p => p.id).includes(p.id)
        ),
      }));
  }
}
