import {
  Body,
  Controller,
  Del,
  Get,
  Inject,
  Param,
  Post,
  Put,
  Query,
} from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { ServiceService } from '../service/service.service';
import { CustomError } from '../error/custom.error';
import { AdditionalService, ServiceType } from '../entity';
import { Op } from 'sequelize';

@Controller('/service')
export class ServiceController {
  @Inject()
  ctx: Context;

  @Inject()
  service: ServiceService;

  @Get('/', { summary: '查询列表' })
  async index(@Query() query: any) {
    let { offset, limit } = query;
    const {
      offset: o,
      limit: l,
      current,
      pageSize,
      filter,
      ...queryInfo
    } = query;
    void o;
    void l;

    if (queryInfo.serviceName) {
      queryInfo.serviceName = {
        [Op.like]: `%${queryInfo.serviceName}%`,
      };
    }

    if (current && pageSize) {
      offset = (current - 1) * pageSize;
      limit = pageSize;
    }
    return this.service.findAll({
      query: queryInfo,
      offset,
      limit,
      filter,
      include: [{ model: ServiceType }, { model: AdditionalService }],
    });
  }

  @Get('/:id', { summary: '按ID查询单个' })
  async show(@Param('id') id: string) {
    const res = await this.service.findById(id);
    if (!res) {
      throw new CustomError('未找到指定信息');
    }
    return res;
  }

  @Post('/edit', { summary: '编辑' })
  async edit(@Query() query: any, @Body() body: any) {
    await this.service.update(query, body);
    return true;
  }

  @Post('/', { summary: '新增' })
  async create(@Body() info: any) {
    const res = await this.service.create(info);
    return res;
  }

  @Put('/:id', { summary: '更新' })
  async update(@Param('id') id: string, @Body() body: any) {
    await this.service.update({ id }, body);
    return true;
  }

  @Del('/:id', { summary: '删除' })
  async destroy(@Param('id') id: string) {
    await this.service.delete({ id });
    return true;
  }

  @Post('/:id/additional-service', { summary: '设置增项服务' })
  async setAdditionalService(
    @Param('id') id: number,
    @Body()
    body: {
      additional_service_ids: number[];
    }
  ) {
    const { additional_service_ids } = body;
    const res = await this.service.setAdditionalService(
      id,
      additional_service_ids
    );
    return res;
  }
}
