<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单 WebSocket 测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 10px;
            font-weight: bold;
        }
        .status.connected {
            background-color: #d4edda;
            color: #155724;
        }
        .status.disconnected {
            background-color: #f8d7da;
            color: #721c24;
        }
        .controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        button {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn-primary { background-color: #007bff; color: white; }
        .btn-success { background-color: #28a745; color: white; }
        .btn-danger { background-color: #dc3545; color: white; }
        .btn-warning { background-color: #ffc107; color: black; }
        button:hover { opacity: 0.8; }
        button:disabled { opacity: 0.5; cursor: not-allowed; }

        .message-log {
            height: 300px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            background-color: #f8f9fa;
            font-family: monospace;
            font-size: 12px;
            margin-bottom: 10px;
        }
        .message-item {
            margin-bottom: 5px;
            padding: 3px;
            border-radius: 3px;
        }
        .message-sent { background-color: #e3f2fd; }
        .message-received { background-color: #f3e5f5; }
        .message-system { background-color: #fff3e0; }

        input, textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-bottom: 10px;
            box-sizing: border-box;
        }
        textarea { height: 60px; resize: vertical; }

        .info {
            display: flex;
            gap: 20px;
            margin-bottom: 10px;
        }
        .info div {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            flex: 1;
        }
    </style>
</head>
<body>
    <h1>简单 WebSocket 测试</h1>

    <div class="container">
        <h2>连接状态</h2>
        <div id="status" class="status disconnected">未连接</div>

        <div class="controls">
            <button id="connectBtn" class="btn-primary">连接</button>
            <button id="disconnectBtn" class="btn-danger" disabled>断开连接</button>
            <button id="clearLogBtn" class="btn-warning">清空日志</button>
            <button id="pingBtn" class="btn-success" disabled>心跳</button>
        </div>

        <div class="info">
            <div>
                <strong>服务器:</strong> ws://localhost:3001<br>
                <strong>客户端ID:</strong> <span id="clientId">-</span>
            </div>
            <div>
                <strong>发送:</strong> <span id="sentCount">0</span><br>
                <strong>接收:</strong> <span id="receivedCount">0</span>
            </div>
        </div>
    </div>

    <div class="container">
        <h2>消息</h2>
        <div class="message-log" id="messageLog"></div>
        <textarea id="messageInput" placeholder="输入消息内容..."></textarea>
        <div class="controls">
            <button id="sendBtn" class="btn-primary" disabled>发送消息</button>
            <button class="btn-success" onclick="sendQuickMessage('Hello!')">Hello</button>
            <button class="btn-success" onclick="sendQuickMessage('测试中文')">测试中文</button>
        </div>
    </div>

    <script>
        let ws = null;
        let clientId = null;
        let sentCount = 0;
        let receivedCount = 0;

        const statusEl = document.getElementById('status');
        const connectBtn = document.getElementById('connectBtn');
        const disconnectBtn = document.getElementById('disconnectBtn');
        const clearLogBtn = document.getElementById('clearLogBtn');
        const pingBtn = document.getElementById('pingBtn');
        const sendBtn = document.getElementById('sendBtn');
        const messageInput = document.getElementById('messageInput');
        const messageLogEl = document.getElementById('messageLog');
        const clientIdEl = document.getElementById('clientId');
        const sentCountEl = document.getElementById('sentCount');
        const receivedCountEl = document.getElementById('receivedCount');

        function updateStatus(status, message) {
            statusEl.className = `status ${status}`;
            statusEl.textContent = message;
        }

        function updateStats() {
            sentCountEl.textContent = sentCount;
            receivedCountEl.textContent = receivedCount;
        }

        function addLogMessage(message, type = 'system') {
            const messageEl = document.createElement('div');
            messageEl.className = `message-item message-${type}`;

            const timestamp = new Date().toLocaleTimeString();
            const content = typeof message === 'object' ? JSON.stringify(message) : message;
            messageEl.innerHTML = `[${timestamp}] ${content}`;

            messageLogEl.appendChild(messageEl);
            messageLogEl.scrollTop = messageLogEl.scrollHeight;
        }

        function connect() {
            if (ws && ws.readyState === WebSocket.OPEN) return;

            updateStatus('connecting', '正在连接...');
            ws = new WebSocket('ws://localhost:3001');

            ws.onopen = function() {
                updateStatus('connected', '已连接');
                connectBtn.disabled = true;
                disconnectBtn.disabled = false;
                sendBtn.disabled = false;
                pingBtn.disabled = false;
                addLogMessage('WebSocket 连接已建立', 'system');
            };

            ws.onmessage = function(event) {
                receivedCount++;
                updateStats();

                try {
                    const data = JSON.parse(event.data);
                    addLogMessage(data, 'received');

                    if (data.type === 'connected' && data.clientId) {
                        clientId = data.clientId;
                        clientIdEl.textContent = clientId;
                    }
                } catch (e) {
                    addLogMessage(event.data, 'received');
                }
            };

            ws.onclose = function(event) {
                updateStatus('disconnected', '连接已断开');
                connectBtn.disabled = false;
                disconnectBtn.disabled = true;
                sendBtn.disabled = true;
                pingBtn.disabled = true;
                addLogMessage('连接关闭', 'system');
            };

            ws.onerror = function() {
                addLogMessage('连接错误', 'system');
            };
        }

        function disconnect() {
            if (ws) ws.close();
        }

        function sendMessage() {
            if (!ws || ws.readyState !== WebSocket.OPEN) {
                addLogMessage('WebSocket 未连接', 'system');
                return;
            }

            const content = messageInput.value.trim();
            if (!content) return;

            const message = { type: 'chat', content };
            ws.send(JSON.stringify(message));
            sentCount++;
            updateStats();
            addLogMessage(message, 'sent');
            messageInput.value = '';
        }

        function sendQuickMessage(content) {
            if (!ws || ws.readyState !== WebSocket.OPEN) {
                addLogMessage('WebSocket 未连接', 'system');
                return;
            }

            const message = { type: 'chat', content };
            ws.send(JSON.stringify(message));
            sentCount++;
            updateStats();
            addLogMessage(message, 'sent');
        }

        function sendPing() {
            if (!ws || ws.readyState !== WebSocket.OPEN) return;

            const message = { type: 'ping' };
            ws.send(JSON.stringify(message));
            sentCount++;
            updateStats();
            addLogMessage(message, 'sent');
        }

        // 事件监听器
        connectBtn.addEventListener('click', connect);
        disconnectBtn.addEventListener('click', disconnect);
        clearLogBtn.addEventListener('click', () => {
            messageLogEl.innerHTML = '';
            sentCount = receivedCount = 0;
            updateStats();
        });
        pingBtn.addEventListener('click', sendPing);
        sendBtn.addEventListener('click', sendMessage);

        messageInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });

        // 页面加载完成
        window.addEventListener('load', function() {
            addLogMessage('页面加载完成，点击"连接"按钮开始测试', 'system');
        });
    </script>
</body>
</html>
