import {
  Table,
  Column,
  Model,
  DataType,
  BelongsTo,
  ForeignKey,
} from 'sequelize-typescript';
import { ProductType } from './product-type.entity';

export interface ProductAttributes {
  /** 商品唯一标识 */
  id: number;
  /** 商品名称 */
  name: string;
  /** 商品编码 */
  SKU?: string;
  /** 商品类别 */
  typeId?: number;
  /** 富文本内容 */
  content?: string;
  /** 库存 */
  stock: number;
  /** 售价 */
  price: number;
  /** 关联的商品类型信息 */
  type?: ProductType;
}

@Table({ tableName: 'products', timestamps: true, comment: '商品表' })
export class Product
  extends Model<ProductAttributes>
  implements ProductAttributes
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '商品唯一标识',
  })
  id: number;

  @Column({
    type: DataType.STRING(100),
    allowNull: false,
    comment: '商品名称',
  })
  name: string;

  @Column({
    type: DataType.STRING(100),
    comment: '商品编码',
  })
  SKU?: string;

  @Column({
    type: DataType.INTEGER,
    comment: '商品类别',
  })
  @ForeignKey(() => ProductType)
  typeId?: number;

  @Column({
    type: DataType.TEXT,
    comment: '富文本内容',
  })
  content?: string;

  @Column({
    type: DataType.INTEGER,
    defaultValue: 0,
    comment: '库存',
  })
  stock: number;

  @Column({
    type: DataType.DECIMAL(8, 2),
    allowNull: false,
    comment: '售价',
  })
  price: number;

  @BelongsTo(() => ProductType, {
    onDelete: 'SET NULL',
    onUpdate: 'CASCADE',
  })
  type?: ProductType;
}
