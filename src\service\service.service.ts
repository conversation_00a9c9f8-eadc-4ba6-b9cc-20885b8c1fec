import { Inject, Provide } from '@midwayjs/core';
import { AdditionalService, Service } from '../entity';
import { BaseService } from '../common/BaseService';
import { Context } from '@midwayjs/koa';
import { CustomError } from '../error/custom.error';

@Provide()
export class ServiceService extends BaseService<Service> {
  @Inject()
  ctx: Context;

  constructor() {
    super('服务项目');
  }
  getModel = () => {
    return Service;
  };

  /**
   * 设置增项服务
   * @param serviceId
   * @param additionalServiceIds
   */
  async setAdditionalService(
    serviceId: number,
    additionalServiceIds: number[]
  ) {
    const service = await this.findById(serviceId);
    if (!service) {
      throw new CustomError('未找到指定服务');
    }
    const additionalServices = await AdditionalService.findAll({
      where: {
        id: additionalServiceIds,
      },
    });
    service.$set('additionalServices', additionalServices);
    await service.save();
    return service;
  }

  /**
   * 获取增项服务
   * @param serviceId
   */
  async getAdditionalService(serviceId: number) {
    const service = await this.findById(serviceId);
    if (!service) {
      throw new CustomError('未找到指定服务');
    }
    const additionalServices = await service.$get('additionalServices');
    return additionalServices;
  }
}
