/*
 * @Description: 代金券相关接口，不用认证
 * @Date: 2025-05-12 22:30:59
 * @LastEditors: ZhuPengliang <EMAIL>
 * @LastEditTime: 2025-05-13 05:40:55
 */
import { Controller, Get, Inject } from '@midwayjs/core';
import { CouponService } from '../../service/coupon.service';

@Controller('/openapi/coupon')
export class ServiceController {
  @Inject()
  service: CouponService;

  @Get('/', { summary: '查询代金券列表' })
  async index() {
    return this.service.findAll({ query: { isEnabled: true } });
  }
}
