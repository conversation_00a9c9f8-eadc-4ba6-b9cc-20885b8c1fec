import {
  Table,
  Column,
  Model,
  DataType,
  BelongsTo,
  ForeignKey,
} from 'sequelize-typescript';
import { CustomerCoupon } from './customer-coupon.entity';
import { Order } from './order.entity';

export interface CouponUsageRecordAttributes {
  /** 使用记录唯一标识 */
  id: number;
  /** 关联的客户代金券 */
  customerCouponId: number;
  /** 使用时间 */
  useTime: Date;
  /** 使用的订单 */
  orderId: number;
  /** 是否已退回 */
  isRefunded: boolean;
  /** 退回时间 */
  refundTime?: Date;
  /** 关联的客户代金券信息 */
  customerCoupon?: CustomerCoupon;
  /** 关联的订单信息 */
  order?: Order;
}

@Table({
  tableName: 'coupon_usage_records',
  timestamps: true,
  comment: '代金券使用记录表',
})
export class CouponUsageRecord
  extends Model<CouponUsageRecordAttributes>
  implements CouponUsageRecordAttributes
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '使用记录唯一标识',
  })
  id: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '关联的客户代金券',
  })
  @ForeignKey(() => CustomerCoupon)
  customerCouponId: number;

  @Column({
    type: DataType.DATE,
    allowNull: false,
    defaultValue: DataType.NOW,
    comment: '使用时间',
  })
  useTime: Date;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '使用的订单',
  })
  @ForeignKey(() => Order)
  orderId: number;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
    defaultValue: false,
    comment: '是否已退回',
  })
  isRefunded: boolean;

  @Column({
    type: DataType.DATE,
    allowNull: true,
    comment: '退回时间',
  })
  refundTime?: Date;

  @BelongsTo(() => CustomerCoupon)
  customerCoupon?: CustomerCoupon;

  @BelongsTo(() => Order, {
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  })
  order?: Order;
}
