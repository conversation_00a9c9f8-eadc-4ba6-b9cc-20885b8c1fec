import { Table, Column, Model, DataType, HasMany } from 'sequelize-typescript';
import { Service } from './service.entity';

export interface ServiceTypeAttributes {
  /** 类目ID */
  id: number;
  /** 服务名称 */
  name: string;
  /** 服务描述 */
  description?: string;
  /** 服务类型 */
  type: string;
  /** 排序值 */
  orderIndex: number;
  /** 关联的服务列表 */
  services?: Service[];
}

@Table({ tableName: 'service_types', timestamps: true, comment: '服务类目表' })
export class ServiceType
  extends Model<ServiceTypeAttributes>
  implements ServiceTypeAttributes
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '类目ID',
  })
  id: number;

  @Column({
    type: DataType.STRING(50),
    allowNull: false,
    comment: '服务名称',
  })
  name: string;

  @Column({
    type: DataType.TEXT,
    comment: '服务描述',
  })
  description: string;

  @Column({
    type: DataType.STRING(50),
    allowNull: false,
    comment: '服务类型',
  })
  type: string;

  @Column({
    type: DataType.INTEGER,
    defaultValue: 0,
    comment: '排序值',
  })
  orderIndex: number;

  @HasMany(() => Service)
  services: Service[];
}
