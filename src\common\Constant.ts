export enum OrderStatus {
  待付款 = '待付款',
  待接单 = '待接单',
  待服务 = '待服务',
  已出发 = '已出发',
  服务中 = '服务中',
  已完成 = '已完成',
  已评价 = '已评价',
  已取消 = '已取消',
  退款中 = '退款中',
  已退款 = '已退款',
}

export enum OrderStatusChangeType {
  下单 = '下单',
  付款 = '付款',
  接单 = '接单',
  派单 = '派单',
  转单 = '转单',
  修改服务时间 = '修改服务时间',
  出发 = '出发',
  开始服务 = '开始服务',
  完成订单 = '完成订单',
  取消订单 = '取消订单',
  申请退款 = '申请退款',
  退款 = '退款',
}

/** 适用范围 */
export enum ApplicableScope {
  不限 = 'all',
  所有服务 = 'allServices',
  指定服务类别 = 'serviceType',
  指定服务品牌 = 'serviceCategory',
  指定服务 = 'service',
  所有商品 = 'allProducts',
  指定商品类别 = 'productCategory',
  指定商品 = 'product',
}
