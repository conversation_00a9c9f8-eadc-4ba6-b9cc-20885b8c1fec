/*
 * @Description: 数据库异常过滤器
 * @Date: 2025-01-15 14:06:24
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON>g <EMAIL>
 * @LastEditTime: 2025-05-11 21:14:46
 */

import { Catch, Logger } from '@midwayjs/core';
import { ILogger } from '@midwayjs/logger';
import { Context } from '@midwayjs/koa';
import { DatabaseError, ValidationError } from 'sequelize';
import { ErrorCode } from '../common/ErrorCode';

@Catch([ValidationError, DatabaseError], {
  matchPrototype: true,
})
export class DatabaseFilter {
  @Logger()
  logger: ILogger;

  async catch(err: ValidationError | DatabaseError, ctx: Context) {
    // 数据库异常会到这里
    ctx.logger.error(err);
    this.logger.error({
      source: 'debug',
      message: '数据库异常',
      details: JSON.stringify(err),
    });

    // 处理 DatabaseError 类型的错误（如外键约束错误）
    if (err instanceof DatabaseError) {
      const errorMessage = err.message || '';
      const errorParent = err.parent || {};
      const errorCode = errorParent['code'] || '';
      const table = errorParent['table'] || '';

      // 外键约束错误
      if (
        errorCode === 'ER_ROW_IS_REFERENCED_2' ||
        errorCode === '23503' ||
        errorMessage.includes('foreign key constraint fails')
      ) {
        // 针对不同表的外键约束错误提供不同的友好提示
        if (
          table === 'membership_card_types' ||
          errorMessage.includes('membership_card_types')
        ) {
          return {
            errCode: ErrorCode.BIZ_ERROR,
            msg: '该权益卡类别已有用户使用记录，无法删除。请先删除相关的用户权益卡记录后再尝试删除此类别。',
          };
        } else if (table === 'coupons' || errorMessage.includes('coupons')) {
          return {
            errCode: ErrorCode.BIZ_ERROR,
            msg: '该代金券已有用户使用记录，无法删除。请先删除相关的用户代金券记录后再尝试删除。',
          };
        } else {
          return {
            errCode: ErrorCode.BIZ_ERROR,
            msg: '该记录已被其他数据引用，无法删除。请先删除相关联的记录后再尝试删除。',
          };
        }
      }

      // 其他数据库错误
      return {
        errCode: ErrorCode.BIZ_ERROR,
        msg: '数据库操作错误，请稍后重试。',
      };
    }

    // 处理 ValidationError 类型的错误
    const firstErr = err.errors[0];
    switch (firstErr.type.toLowerCase()) {
      case 'unique violation':
        return {
          errCode: ErrorCode.BIZ_ERROR,
          msg: `${firstErr.value}已存在，操作失败！`,
        };
      case 'notnull violation':
        return {
          errCode: ErrorCode.BIZ_ERROR,
          msg: `${firstErr.path}不能为空，操作失败!`,
        };
      case 'foreignkey constraint':
        // 针对不同字段的外键约束错误提供不同的友好提示
        if (firstErr.path === 'cardTypeId') {
          return {
            errCode: ErrorCode.BIZ_ERROR,
            msg: `权益卡类型不存在，操作失败!`,
          };
        } else if (firstErr.path === 'couponId') {
          return {
            errCode: ErrorCode.BIZ_ERROR,
            msg: `代金券不存在，操作失败!`,
          };
        } else {
          return {
            errCode: ErrorCode.BIZ_ERROR,
            msg: `${firstErr.path}不存在，操作失败!`,
          };
        }
      case 'data type mismatch':
        return {
          errCode: ErrorCode.BIZ_ERROR,
          msg: `${firstErr.path}数据类型不匹配，操作失败!`,
        };
      case 'length violation':
        return {
          errCode: ErrorCode.BIZ_ERROR,
          msg: `${firstErr.path}长度不匹配，操作失败!`,
        };
      case 'string violation':
        return {
          errCode: ErrorCode.BIZ_ERROR,
          msg: `${firstErr.path}格式不匹配，操作失败!`,
        };
      default:
        console.log('数据库校验错误:', firstErr.type.toLowerCase());
        return {
          errCode: ErrorCode.BIZ_ERROR,
          msg: '数据库校验错误，操作失败!',
        };
    }
  }
}
