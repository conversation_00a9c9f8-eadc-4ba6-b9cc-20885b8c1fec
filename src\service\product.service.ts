import { Provide } from '@midwayjs/core';
import { Product } from '../entity/product.entity';
import { BaseService } from '../common/BaseService';

@Provide()
export class ProductService extends BaseService<Product> {
  constructor() {
    super('商品');
  }

  getModel() {
    return Product;
  }

  async findByName(name: string) {
    return await this.findOne({ where: { name } });
  }

  async findBySKU(sku: string) {
    return await this.findOne({ where: { SKU: sku } });
  }

  async findByType(typeId: number) {
    return await this.findAll({
      query: { typeId },
      include: ['type'],
    });
  }

  async updateStock(id: number, stock: number) {
    const product = await this.findById(id);
    if (!product) {
      throw new Error('商品不存在');
    }
    return await product.update({
      stock: product.stock + stock,
    });
  }

  async updatePrice(id: number, price: number) {
    return await this.update({ id }, { price });
  }

  async findInStock() {
    return await this.findAll({
      query: {
        stock: { $gt: 0 },
      },
      include: ['type'],
    });
  }
}
