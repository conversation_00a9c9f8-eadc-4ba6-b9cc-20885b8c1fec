import {
  Body,
  Controller,
  Del,
  Get,
  Inject,
  Param,
  Post,
  Query,
} from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { AttendanceService } from '../service/attendance.service';
import { CustomError } from '../error/custom.error';

@Controller('/attendances')
export class AttendanceController {
  @Inject()
  ctx: Context;

  @Inject()
  service: AttendanceService;

  @Get('/', { summary: '查询考勤列表' })
  async index(@Query() query: any) {
    let { offset, limit } = query;
    const { offset: o, limit: l, current, pageSize, ...queryInfo } = query;
    void o;
    void l;

    if (current && pageSize) {
      offset = (current - 1) * pageSize;
      limit = pageSize;
    }

    return this.service.findAll({
      query: queryInfo,
      offset,
      limit,
      include: ['employee', 'vehicle'],
    });
  }

  @Get('/:id', { summary: '按ID查询考勤' })
  async show(@Param('id') id: string) {
    const res = await this.service.findById(id);
    if (!res) {
      throw new CustomError('未找到指定考勤记录');
    }
    return res;
  }

  @Post('/', { summary: '新增考勤' })
  async create(@Body() info: any) {
    const res = await this.service.create(info);
    return res;
  }

  @Del('/:id', { summary: '删除考勤' })
  async destroy(@Param('id') id: string) {
    await this.service.delete({ id });
    return true;
  }

  @Get('/employee/:employeeId', { summary: '查询员工考勤记录' })
  async findByEmployee(@Param('employeeId') employeeId: number) {
    return await this.service.findByEmployee(employeeId);
  }

  @Get('/date/:startDate/:endDate', { summary: '按日期查询考勤记录' })
  async findByDate(
    @Param('startDate') startDate: string,
    @Param('endDate') endDate: string
  ) {
    return await this.service.findByDate(
      new Date(startDate),
      new Date(endDate)
    );
  }
}
