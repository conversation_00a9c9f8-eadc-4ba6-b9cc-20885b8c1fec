import { Provide, Inject, Logger } from '@midwayjs/core';
import { ILogger } from '@midwayjs/logger';
import { Job, IJob } from '@midwayjs/cron';
import { OrderService } from '../service/order.service';
import { OrderDiscountInfoService } from '../service/order-discount-info.service';
import { Order } from '../entity/order.entity';
import { OrderStatus } from '../common/Constant';
import { Op } from 'sequelize';
import { OrderDiscountInfo } from '../entity';

@Provide()
@Job('orderDiscountRefundJob', {
  cronTime: '*/15 * * * *', // 每15分钟执行一次
  start: true, // 应用启动时自动启动任务
})
export class OrderDiscountRefundJob implements IJob {
  @Inject()
  orderService: OrderService;

  @Inject()
  orderDiscountInfoService: OrderDiscountInfoService;

  @Logger()
  logger: ILogger;

  async onTick() {
    this.logger.info('开始执行订单优惠退回定时任务...');
    console.log('开始执行订单优惠退回定时任务...');

    try {
      const expiredOrders = await Order.findAll({
        where: {
          status: OrderStatus.待付款,
          createdAt: {
            [Op.lt]: new Date(Date.now() - 15 * 60 * 1000), // 15分钟前创建的
          },
          [Op.or]: [
            { cardDeduction: { [Op.gt]: 0 } },
            { couponDeduction: { [Op.gt]: 0 } },
          ],
        },
        include: [
          {
            model: OrderDiscountInfo,
            where: {
              isRefunded: false,
            },
            required: true,
          },
        ],
      });

      this.logger.info(`找到 ${expiredOrders.length} 个需要处理的过期订单`);
      console.log(`找到 ${expiredOrders.length} 个需要处理的过期订单`);

      let processedCount = 0;
      let refundedCount = 0;

      // 处理每个过期订单
      for (const order of expiredOrders) {
        // 检查订单是否已经处理过退款
        const refundStatus =
          await this.orderDiscountInfoService.checkOrderRefundStatus(order.id);

        // 如果订单有优惠信息且未全部退回，则处理退回
        if (refundStatus.hasDiscountInfos && !refundStatus.allRefunded) {
          const result =
            await this.orderDiscountInfoService.refundOrderDiscounts(
              order.id,
              '订单过期自动退回'
            );
          refundedCount += result.refundedCount;
        }

        processedCount++;
      }

      this.logger.info(
        `处理完成，共处理 ${processedCount} 个订单，退回 ${refundedCount} 个优惠`
      );
      console.log(
        `处理完成，共处理 ${processedCount} 个订单，退回 ${refundedCount} 个优惠`
      );
    } catch (error) {
      this.logger.error('订单优惠退回定时任务执行失败:', error);
      console.error('订单优惠退回定时任务执行失败:', error);
    }
  }
}
