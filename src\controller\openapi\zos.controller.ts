import { Body, Controller, Get, Inject, Post, Query } from '@midwayjs/core';
import { ZOSService } from '../../service/zos.service';

@Controller('/openapi/zos')
export class ZosController {
  @Inject()
  zosService: ZOSService;

  // @Get('/sign')
  // async generateSign() {
  //   const res = this.zosService.generateSign();
  //   console.log(res);
  //   return true;
  // }

  @Get('/upload-link', { summary: '生成对象上传链接' })
  async getObjectUploadLink(@Query('key') key: string) {
    const res = await this.zosService.getObjectUploadLink(key);
    return res;
  }

  @Post('/set-object-headers', { summary: '设置对象的 HTTP 头' })
  async setObjectHeaders(
    @Body() { key, ContentType }: { key: string; ContentType: string }
  ) {
    return this.zosService.setObjectHeaders(key, ContentType);
  }

  @Post('/set-object-acl', { summary: '设置对象ACL' })
  async setObjectACL(
    @Body()
    {
      key,
      ACL,
    }: {
      key: string;
      ACL:
        | 'private'
        | 'public-read'
        | 'public-read-write'
        | 'authenticated-read';
    }
  ) {
    return this.zosService.setObjectACL(key, ACL);
  }

  @Get('/list-objects', { summary: '查询存储桶内所有对象' })
  async getRegions() {
    return this.zosService.getRegions();
  }
}
