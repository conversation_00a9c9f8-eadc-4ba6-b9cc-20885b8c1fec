import {
  Table,
  Column,
  Model,
  DataType,
  ForeignKey,
  BelongsTo,
  BelongsToMany,
} from 'sequelize-typescript';
import { Feature } from './feature.entity';
import { Role } from './role.entity';
import { RolePermission } from './role_permission.entity';

export interface PermissionAttributes {
  /** 权限ID */
  id: number;
  /** 权限名称 */
  name: string;
  /** 权限描述 */
  description?: string;
  /** 关联的功能编号 */
  featureCode: string;
  /** 关联的角色列表 */
  roles?: Role[];
}

@Table({ tableName: 'permissions', timestamps: true, comment: '权限表' })
export class Permission
  extends Model<PermissionAttributes>
  implements PermissionAttributes
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '权限ID',
  })
  id: number;

  @Column({
    type: DataType.STRING,
    allowNull: false,
    unique: {
      name: 'unique_name_feature',
      msg: '该功能下已存在相同名称的权限',
    },
    comment: '权限名称',
  })
  name: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
    comment: '权限描述',
  })
  description: string;

  @ForeignKey(() => Feature)
  @Column({
    type: DataType.STRING,
    allowNull: false,
    unique: {
      name: 'unique_name_feature',
      msg: '该功能下已存在相同名称的权限',
    },
    comment: '关联的功能编号',
  })
  featureCode: string;

  @BelongsTo(() => Feature)
  feature: Feature;

  @BelongsToMany(() => Role, () => RolePermission)
  roles: Role[];
}
