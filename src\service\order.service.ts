import { ILogger, Inject, Provide } from '@midwayjs/core';
import {
  AdditionalService,
  Customer,
  Employee,
  Order,
  OrderDetail,
  Service,
  ServiceChangeLog,
  ServiceType,
} from '../entity';
import { BaseService } from '../common/BaseService';
import { Context } from '@midwayjs/koa';
import * as lodash from 'lodash';
import { CustomError } from '../error/custom.error';
import { OrderStatus, OrderStatusChangeType } from '../common/Constant';
import { WepayService } from './wepay.service';
import { OrderDiscountInfoService } from './order-discount-info.service';
import { WebSocketService } from './websocket.service';

@Provide()
export class OrderService extends BaseService<Order> {
  @Inject()
  ctx: Context;

  @Inject()
  wepayService: WepayService;

  @Inject()
  orderDiscountInfoService: OrderDiscountInfoService;

  @Inject()
  webSocketService: WebSocketService;

  @Inject()
  logger: ILogger;

  constructor() {
    super('订单');
  }
  getModel = () => {
    return Order;
  };

  /**
   * 获取订单日志
   *
   * @param orderId 订单ID
   * @returns 日志
   */
  async getLogs(orderId: number) {
    return await ServiceChangeLog.findAll({
      where: {
        orderId,
      },
      include: [
        {
          model: Customer,
        },
        {
          model: Employee,
        },
      ],
      order: [['createdAt', 'DESC']],
    });
  }

  /**
   * 创建订单
   *
   * @param {*} query 查询条件
   * @memberof OrderService
   */
  async createOrder(body: CreateOrderData) {
    // TODO: 要考虑数据量大的时候使用分页查询
    const { orderDetails, discountInfos, ...createInfo } = body;
    // 生成订单编号：当前时间戳 + 4位随机数
    const timestamp = Date.now().toString();
    const random = Math.floor(Math.random() * 10000)
      .toString()
      .padStart(4, '0');
    createInfo['sn'] = `${timestamp}${random}`;
    // 使用事务创建订单及订单详情
    const result = await Order.sequelize.transaction(async t => {
      console.log('createInfo', createInfo);
      const order = await Order.create(
        {
          ...createInfo,
          serviceTime: !createInfo.serviceTime ? null : createInfo.serviceTime,
        },
        { transaction: t }
      );

      await Promise.all(
        orderDetails.map(async orderDetail => {
          const { addServiceIds, ...addInfo } = orderDetail;
          const detail = await OrderDetail.create(
            {
              ...addInfo,
              orderId: order.id,
            },
            { transaction: t }
          );
          if (addServiceIds?.length) {
            console.log('addServiceIds', addServiceIds);
            const flattenedServiceIds = lodash.flattenDeep(addServiceIds);
            console.log('flattenedServiceIds', flattenedServiceIds);
            await detail.$set('additionalServices', flattenedServiceIds, {
              transaction: t,
            });
          }
        })
      );

      ServiceChangeLog.create({
        orderId: order.id,
        changeType: OrderStatusChangeType.下单,
        customerId: createInfo.customerId,
      });

      return order;
    });

    // 处理卡券使用和优惠信息创建
    if (discountInfos && discountInfos.length > 0) {
      try {
        await this.orderDiscountInfoService.processOrderDiscounts(
          result.id,
          discountInfos
        );
      } catch (error) {
        this.logger.error('处理订单优惠信息失败:', error);
      }
    }

    return result;
  }

  // 支付订单
  async payOrder(customerId: number, sn: string) {
    const order = await this.findOne({ where: { sn } });
    if (!order) {
      throw new CustomError('订单不存在');
    }
    if (order.status !== OrderStatus.待付款) {
      throw new CustomError('订单状态不正确');
    }
    await order.update({
      status: OrderStatus.待接单,
    });
    await ServiceChangeLog.create({
      orderId: order.id,
      changeType: OrderStatusChangeType.付款,
      customerId,
    });

    // 广播新订单消息
    await this.broadcastNewOrder(order);

    return true;
  }

  // 申请退款
  async applyRefund(customerId: number, sn: string) {
    const order = await this.findOne({ where: { sn } });
    if (!order) {
      throw new CustomError('订单不存在');
    }
    console.log('order', order);
    if (
      !([OrderStatus.待服务, OrderStatus.已出发] as string[]).includes(
        order.status
      )
    ) {
      throw new CustomError('订单状态不正确');
    }
    await order.update({
      status: OrderStatus.退款中,
    });
    await ServiceChangeLog.create({
      orderId: order.id,
      changeType: OrderStatusChangeType.申请退款,
      customerId,
    });
    return true;
  }

  /**
   * 审核退款
   *
   * @param {object} params 查询条件
   * @param {string} params.sn 订单编号
   * @param {boolean} params.result 审核结果
   * @param {string} [params.reason] 审核原因，审核不通过时必填
   * @param {number} [params.money] 退款金额，审核通过时必填
   * @returns res
   */
  async auditRefund({
    sn,
    result,
    reason,
    money,
  }: {
    sn: string;
    result: boolean;
    reason?: string;
    money?: number;
  }) {
    const order = await this.findOne({ where: { sn } });
    if (!order) {
      throw new CustomError('订单不存在');
    }
    if (order.status !== OrderStatus.退款中) {
      throw new CustomError('订单状态不正确');
    }
    if (result) {
      // 处理卡券退回
      try {
        // 检查订单是否有优惠信息
        const refundStatus =
          await this.orderDiscountInfoService.checkOrderRefundStatus(order.id);
        if (refundStatus.hasDiscountInfos && !refundStatus.allRefunded) {
          await this.orderDiscountInfoService.refundOrderDiscounts(
            order.id,
            `退款审核通过，退款金额${money}元`
          );
        }
      } catch (error) {
        this.logger.error('处理订单卡券退回失败:', error);
      }

      await this.wepayService.refund(sn, money);
      await order.update({
        status: OrderStatus.已退款,
      });
      await ServiceChangeLog.create({
        changeType: OrderStatusChangeType.退款,
        orderId: order.id,
        description: `退款成功，退款金额${money}元。操作人：${this.ctx.state.user.name}`,
      });
    } else {
      await order.update({
        status: OrderStatus.待服务,
      });
      await ServiceChangeLog.create({
        changeType: OrderStatusChangeType.退款,
        orderId: order.id,
        description: `退款失败，原因：${reason} 操作人：${this.ctx.state.user.name}`,
      });
    }
  }

  /**
   * 取消订单
   * 提前六个小时，扣除20%，提前四个小时，扣除40%，提前两个小时扣除60%，到点取消扣除80%。
   *
   * @param {*} id 订单ID
   * @param customerId 用户ID
   * @param id 订单ID
   * @returns res
   */
  async cancelOrder(customerId: number, id: number) {
    const order = await this.findById(id);
    if (!order) {
      throw new CustomError('订单不存在');
    }
    if (order.status !== OrderStatus.待付款) {
      throw new CustomError('目前仅能自助取消待支付订单，特殊情况请联系客服');
    }

    // 处理卡券退回
    try {
      // 检查订单是否有优惠信息
      const refundStatus =
        await this.orderDiscountInfoService.checkOrderRefundStatus(id);
      if (refundStatus.hasDiscountInfos && !refundStatus.allRefunded) {
        await this.orderDiscountInfoService.refundOrderDiscounts(
          id,
          customerId ? '用户取消订单' : '系统取消订单'
        );
      }
    } catch (error) {
      this.logger.error('处理订单卡券退回失败:', error);
    }

    await order.update({
      status: OrderStatus.已取消,
    });
    await ServiceChangeLog.create({
      orderId: order.id,
      changeType: OrderStatusChangeType.取消订单,
      customerId: customerId || null,
      description: customerId ? '用户取消订单' : '系统取消订单',
    });
    return true;
  }

  /**
   * 删除订单
   * 一般只有具备足够权限的用户，如系统管理员或高级用户才能执行删除订单操作
   * 很多系统限制只能删除特定状态的订单，如未支付的订单。
   * 已支付、配送中、已完成的订单通常不支持删除。
   * 通过在线结账系统处理付款的订单或使用礼品卡付款的订单，只能存档不能删除
   */
  async deleteOrder(id: number) {
    console.log('id', id);
    const order = await this.findById(id);
    if (!order) {
      throw new CustomError('订单不存在');
    }
    if (order.status !== OrderStatus.已取消) {
      throw new CustomError('订单状态不正确');
    }
    return await order.destroy();
    // throw new CustomError('暂不支持删除订单');
  }

  // 查询可接单列表
  // list: '/orders',
  async findList(query: any) {
    const { page = 1, pageSize = 10, type, ...rest } = query;
    void rest;
    const where_orderDetail = {};
    if (type) {
      const services = await Service.findAll({
        where: {
          published: true,
        },
        attributes: ['id'],
        include: [
          {
            model: ServiceType,
            where: {
              type,
            },
            attributes: ['id'],
          },
        ],
      });
      where_orderDetail['serviceId'] = services.map(s => s.id);
    }

    const res = await Order.findAndCountAll({
      where: {
        status: OrderStatus.待接单,
      },
      offset: (page - 1) * pageSize,
      limit: pageSize,
      include: [
        {
          model: OrderDetail,
          where: where_orderDetail,
          include: [
            {
              model: AdditionalService,
              through: {
                attributes: [],
              },
            },
            {
              model: Service,
            },
          ],
        },
        {
          model: Customer,
        },
      ],
    });
    return {
      list: res.rows,
      total: res.count,
    };
  }

  // 按状态查询员工名下的订单列表
  async findEmployeeOrders({
    employeeId,
    status,
    page,
    pageSize,
  }: {
    employeeId: number;
    status: OrderStatus[];
    page?: number;
    pageSize?: number;
  }) {
    console.log('status:', status);
    return await this.findAll({
      query: {
        employeeId,
        status,
      },
      offset: page && pageSize ? (page - 1) * pageSize : undefined,
      limit: pageSize || undefined,
      include: [
        {
          model: OrderDetail,
          include: [
            {
              model: AdditionalService,
              through: {
                attributes: [],
              },
            },
            {
              model: Service,
            },
          ],
        },
        {
          model: Customer,
        },
      ],
    });
  }

  // 修改服务时间
  async updateServiceTime({
    id,
    serviceTime,
    employeeId,
  }: {
    id: number;
    serviceTime: Date;
    employeeId?: number;
  }) {
    const order = await this.findById(id);
    if (!order) {
      throw new CustomError('订单不存在');
    }
    if (order.status !== OrderStatus.待服务) {
      throw new CustomError('订单状态不正确');
    }
    await order.update({
      serviceTime,
    });
    await ServiceChangeLog.create({
      orderId: order.id,
      employeeId,
      changeType: OrderStatusChangeType.修改服务时间,
      description: employeeId ? '员工修改服务时间' : '系统修改服务时间',
    });
    return true;
  }

  // 接单
  // accept: '/orders/{orderId}/accept',
  async acceptOrder(id: number, employeeId: number) {
    const order = await this.findById(id);
    if (!order) {
      throw new CustomError('订单不存在');
    }
    if (![OrderStatus.待接单].includes(order.status as OrderStatus)) {
      throw new CustomError('订单状态不正确');
    }
    await order.update({
      status: OrderStatus.待服务,
      employeeId,
    });
    await ServiceChangeLog.create({
      orderId: order.id,
      changeType: OrderStatusChangeType.接单,
      employeeId,
    });
    return true;
  }

  // 派单
  async deliverOrder(id: number, employeeId: number) {
    const order = await this.findById(id);
    if (!order) {
      throw new CustomError('订单不存在');
    }
    if (![OrderStatus.待接单].includes(order.status as OrderStatus)) {
      throw new CustomError('订单状态不正确');
    }
    await order.update({
      status: OrderStatus.待服务,
      employeeId,
    });
    await ServiceChangeLog.create({
      orderId: order.id,
      changeType: OrderStatusChangeType.派单,
      employeeId,
      description: '系统派单',
    });
    return true;
  }

  // 转单
  async transferOrder(id: number, employeeId: number) {
    const order = await this.findById(id);
    if (!order) {
      throw new CustomError('订单不存在');
    }
    if (![OrderStatus.待服务].includes(order.status as OrderStatus)) {
      throw new CustomError('订单状态不正确');
    }
    await order.update({
      status: OrderStatus.待服务,
      employeeId,
    });
    await ServiceChangeLog.create({
      orderId: order.id,
      changeType: OrderStatusChangeType.转单,
      employeeId,
      description: '系统转单',
    });
    return true;
  }

  // 出发
  // dispatch: '/orders/{orderId}/dispatch',
  async order_dispatch(id: number, employeeId: number) {
    const order = await this.findById(id);
    if (!order) {
      throw new CustomError('订单不存在');
    }
    // 判断订单是不是这个员工的
    if (order.employeeId !== employeeId) {
      throw new CustomError('订单不属于该员工');
    }
    if (![OrderStatus.待服务].includes(order.status as OrderStatus)) {
      throw new CustomError('订单状态不正确');
    }
    await order.update({
      status: OrderStatus.已出发,
      employeeId,
    });
    await ServiceChangeLog.create({
      orderId: order.id,
      changeType: OrderStatusChangeType.出发,
      employeeId,
    });
    return true;
  }

  // 开始服务
  // start: '/orders/{orderId}/start',
  async order_start(id: number, employeeId: number) {
    const order = await this.findById(id);
    if (!order) {
      throw new CustomError('订单不存在');
    }
    // 判断订单是不是这个员工的
    if (order.employeeId !== employeeId) {
      throw new CustomError('订单不属于该员工');
    }
    if (
      ![OrderStatus.已出发, OrderStatus.待服务].includes(
        order.status as OrderStatus
      )
    ) {
      throw new CustomError('订单状态不正确');
    }
    await order.update({
      status: OrderStatus.服务中,
      employeeId,
    });
    await ServiceChangeLog.create({
      orderId: order.id,
      changeType: OrderStatusChangeType.开始服务,
      employeeId,
    });
    return true;
  }

  // 完成订单
  // complete: '/orders/{orderId}/complete',
  async order_complete(id: number, employeeId: number) {
    const order = await this.findById(id);
    if (!order) {
      throw new CustomError('订单不存在');
    }
    // 判断订单是不是这个员工的
    if (order.employeeId !== employeeId) {
      throw new CustomError('订单不属于该员工');
    }
    if (![OrderStatus.服务中].includes(order.status as OrderStatus)) {
      throw new CustomError('订单状态不正确');
    }
    await order.update({
      status: OrderStatus.已完成,
      employeeId,
    });
    await ServiceChangeLog.create({
      orderId: order.id,
      changeType: OrderStatusChangeType.完成订单,
      employeeId,
    });
    return true;
  }

  // 评价订单

  // 投诉订单

  // 退款订单
  // 平台或商家会规定具体的退款政策，如按比例退款、全额退款等。比如美团民宿，房客在 "全额退款日" 之前取消预订，可全额退还线上支付的全部订单金额及押金费用，之后取消则按房东规定的交易规则执行，扣除部分 / 全部订单金额后返还剩余部分

  // 统计订单

  // 导出订单

  /**
   * 广播新订单消息
   * @param order 订单信息
   */
  async broadcastNewOrder(order: Order) {
    try {
      // 获取订单详细信息
      const orderWithDetails = await Order.findByPk(order.id, {
        include: [
          { model: Customer, as: 'customer' },
          { model: OrderDetail, as: 'orderDetails', include: [Service] },
        ],
      });

      if (!orderWithDetails) {
        this.logger.warn(`广播新订单消息失败：订单 ${order.id} 不存在`);
        return;
      }

      // 构造广播消息
      const message = {
        type: 'new_order',
        data: {
          orderId: orderWithDetails.id,
          orderSn: orderWithDetails.sn,
          customerName: orderWithDetails.customer?.nickname || '未知客户',
          customerPhone: orderWithDetails.customer?.phone || '',
          serviceTime: orderWithDetails.serviceTime,
          address: orderWithDetails.address,
          totalFee: orderWithDetails.totalFee,
          services: orderWithDetails.orderDetails?.map(detail => ({
            serviceName: detail.service?.serviceName || '未知服务',
            petName: detail.petName,
            petType: detail.petType,
          })) || [],
          orderTime: orderWithDetails.orderTime,
          status: orderWithDetails.status,
        },
        timestamp: Date.now(),
      };

      // 广播消息
      await this.webSocketService.broadcastToAll(message);
      this.logger.info(`成功广播新订单消息：订单 ${order.sn}`);
    } catch (error) {
      this.logger.error(`广播新订单消息失败：`, error);
    }
  }
}
