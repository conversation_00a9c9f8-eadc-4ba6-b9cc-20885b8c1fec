import {
  Table,
  Column,
  Model,
  DataType,
  ForeignKey,
} from 'sequelize-typescript';
import { OrderDetail } from './order-detail.entity';
import { AdditionalService } from './additional-service.entity';

@Table({
  tableName: 'order_detail_additional',
  timestamps: true,
  comment: '订单详情-增项服务关联表',
})
export class OrderDetailAdditional extends Model {
  @ForeignKey(() => OrderDetail)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    unique: {
      name: 'unique_order_detail_additional',
      msg: '已存在相同订单详情-增项服务关联',
    },
    comment: '订单详情ID',
  })
  odId: number;

  @ForeignKey(() => AdditionalService)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    unique: {
      name: 'unique_order_detail_additional',
      msg: '已存在相同订单详情-增项服务关联',
    },
    comment: '增项服务ID',
  })
  aId: number;
}
