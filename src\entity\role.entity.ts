import {
  Table,
  Column,
  Model,
  DataType,
  BelongsToMany,
} from 'sequelize-typescript';
import { User } from './user.entity';
import { UserRole } from './user_role.entity';
import { Permission } from './permission.entity';
import { RolePermission } from './role_permission.entity';

export interface RoleAttributes {
  /** 角色ID */
  id: number;
  /** 角色名称 */
  name: string;
  /** 角色描述 */
  description?: string;
  /** 关联的用户列表 */
  users: User[];
  /** 关联的权限列表 */
  permissions: Permission[];
}

@Table({ tableName: 'roles', timestamps: true, comment: '角色表' })
export class Role extends Model<RoleAttributes> implements RoleAttributes {
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '角色ID',
  })
  id: number;

  @Column({
    type: DataType.STRING,
    allowNull: false,
    unique: {
      name: 'name',
      msg: '已存在相同的角色名称',
    },
    comment: '角色名称',
  })
  name: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
    comment: '角色描述',
  })
  description: string;

  @BelongsToMany(() => User, () => UserRole)
  users: User[];

  @BelongsToMany(() => Permission, () => RolePermission)
  permissions: Permission[];
}
