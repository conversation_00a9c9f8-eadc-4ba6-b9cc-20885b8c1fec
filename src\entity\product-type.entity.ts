import {
  Table,
  Column,
  Model,
  DataType,
  BelongsTo,
  HasMany,
} from 'sequelize-typescript';
import { Product } from './product.entity';

export interface ProductTypeAttributes {
  /** 唯一标识 */
  id: number;
  /** 类别名称 */
  name: string;
  /** 排序值 */
  orderIndex: number;
  /** 父类别 */
  parentId?: number;
  /** 图标 */
  icon?: string;
  /** 父级类别信息 */
  parent?: ProductType;
  /** 子级类别列表 */
  children?: ProductType[];
  /** 关联的商品列表 */
  products?: Product[];
}

@Table({ tableName: 'product_types', timestamps: true, comment: '商品类别表' })
export class ProductType
  extends Model<ProductTypeAttributes>
  implements ProductTypeAttributes
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '唯一标识',
  })
  id: number;

  @Column({
    type: DataType.STRING(100),
    allowNull: false,
    comment: '类别名称',
  })
  name: string;

  @Column({
    type: DataType.INTEGER,
    defaultValue: 0,
    comment: '排序值',
  })
  orderIndex: number;

  @Column({
    type: DataType.INTEGER,
    comment: '父类别',
  })
  parentId?: number;

  @Column({
    type: DataType.STRING(255),
    comment: '图标',
  })
  icon?: string;

  @BelongsTo(() => ProductType, 'parentId')
  parent?: ProductType;

  @HasMany(() => ProductType, 'parentId')
  children?: ProductType[];

  @HasMany(() => Product)
  products?: Product[];
}
