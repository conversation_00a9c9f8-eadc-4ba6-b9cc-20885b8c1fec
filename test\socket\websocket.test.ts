import { createApp, close, createWebSocketClient } from '@midwayjs/mock';
import { Framework } from '@midwayjs/koa';
import { sleep } from '@midwayjs/core';
import { once } from 'events';

describe('简单 WebSocket 测试', () => {
  let app: any;

  beforeAll(async () => {
    app = await createApp<Framework>();
  });

  afterAll(async () => {
    await close(app);
  });

  it('应该能够建立WebSocket连接', async () => {
    const client = await createWebSocketClient('ws://localhost:3001');
    await sleep(100);
    expect(client.readyState).toBe(1); // WebSocket.OPEN
    await client.close();
  });

  it('应该能够接收连接成功消息', async () => {
    const client = await createWebSocketClient('ws://localhost:3001');

    const [data] = await once(client, 'message');
    const message = JSON.parse(data.toString());

    expect(message.type).toBe('connected');
    expect(message).toHaveProperty('clientId');
    expect(message.message).toBe('连接成功');

    await client.close();
  });

  it('应该能够发送和接收文本消息', async () => {
    const client = await createWebSocketClient('ws://localhost:3001');

    // 等待连接消息
    await once(client, 'message');

    // 发送文本消息
    const chatMessage = {
      type: 'chat',
      content: 'Hello WebSocket!'
    };
    client.send(JSON.stringify(chatMessage));

    // 等待响应消息
    const [responseData] = await once(client, 'message');
    const response = JSON.parse(responseData.toString());

    expect(response.type).toBe('message');
    expect(response.content).toBe('Hello WebSocket!');
    expect(response).toHaveProperty('clientId');
    expect(response).toHaveProperty('timestamp');

    await client.close();
  });

  it('应该能够处理心跳消息', async () => {
    const client = await createWebSocketClient('ws://localhost:3001');

    await once(client, 'message');

    const pingMessage = { type: 'ping' };
    client.send(JSON.stringify(pingMessage));

    const [responseData] = await once(client, 'message');
    const response = JSON.parse(responseData.toString());

    expect(response.type).toBe('pong');
    expect(response).toHaveProperty('timestamp');

    await client.close();
  });

  it('应该能够处理纯文本消息', async () => {
    const client = await createWebSocketClient('ws://localhost:3001');

    await once(client, 'message');

    // 发送纯文本
    client.send('Hello World!');

    const [responseData] = await once(client, 'message');
    const response = JSON.parse(responseData.toString());

    expect(response.type).toBe('message');
    expect(response.content).toBe('Hello World!');

    await client.close();
  });

  it('应该能够处理多个客户端广播', async () => {
    const client1 = await createWebSocketClient('ws://localhost:3001');
    const client2 = await createWebSocketClient('ws://localhost:3001');

    // 等待连接消息
    await once(client1, 'message');
    await once(client2, 'message');

    // 客户端1发送消息
    const message = { type: 'chat', content: 'Hello from client 1!' };
    client1.send(JSON.stringify(message));

    // 两个客户端都应该收到广播消息
    const [client1Response] = await once(client1, 'message');
    const [client2Response] = await once(client2, 'message');

    const client1Data = JSON.parse(client1Response.toString());
    const client2Data = JSON.parse(client2Response.toString());

    expect(client1Data.type).toBe('message');
    expect(client2Data.type).toBe('message');
    expect(client1Data.content).toBe('Hello from client 1!');
    expect(client2Data.content).toBe('Hello from client 1!');

    await client1.close();
    await client2.close();
  });
});
