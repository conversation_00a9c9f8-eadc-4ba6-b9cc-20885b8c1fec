import { createApp, close, createWebSocketClient } from '@midwayjs/mock';
import { Framework } from '@midwayjs/koa';
import { sleep } from '@midwayjs/core';
import { once } from 'events';

describe('WebSocket 测试', () => {
  let app: any;

  beforeAll(async () => {
    // 创建应用
    app = await createApp<Framework>();
  });

  afterAll(async () => {
    // 关闭应用
    await close(app);
  });

  it('应该能够建立WebSocket连接', async () => {
    // 创建WebSocket客户端
    const client = await createWebSocketClient('ws://localhost:3001');
    
    // 等待连接建立
    await sleep(100);
    
    // 验证连接状态
    expect(client.readyState).toBe(1); // WebSocket.OPEN
    
    // 关闭客户端连接
    await client.close();
  });

  it('应该能够接收连接成功消息', async () => {
    const client = await createWebSocketClient('ws://localhost:3001');
    
    // 等待连接消息
    const [data] = await once(client, 'message');
    const message = JSON.parse(data.toString());
    
    // 验证连接消息
    expect(message.type).toBe('connection');
    expect(message.data).toHaveProperty('clientId');
    expect(message.data.message).toBe('连接成功');
    
    await client.close();
  });

  it('应该能够发送和接收聊天消息', async () => {
    const client = await createWebSocketClient('ws://localhost:3001');
    
    // 等待连接消息
    await once(client, 'message');
    
    // 发送聊天消息
    const chatMessage = {
      type: 'chat',
      content: 'Hello WebSocket!'
    };
    client.send(JSON.stringify(chatMessage));
    
    // 等待响应消息
    const [responseData] = await once(client, 'message');
    const response = JSON.parse(responseData.toString());
    
    // 验证响应
    expect(response.type).toBe('chat');
    expect(response.data.content).toBe('Hello WebSocket!');
    expect(response.data).toHaveProperty('clientId');
    expect(response.data).toHaveProperty('timestamp');
    
    await client.close();
  });

  it('应该能够处理心跳消息', async () => {
    const client = await createWebSocketClient('ws://localhost:3001');
    
    // 等待连接消息
    await once(client, 'message');
    
    // 发送心跳消息
    const pingMessage = { type: 'ping' };
    client.send(JSON.stringify(pingMessage));
    
    // 等待心跳响应
    const [responseData] = await once(client, 'message');
    const response = JSON.parse(responseData.toString());
    
    // 验证心跳响应
    expect(response.type).toBe('pong');
    expect(response).toHaveProperty('timestamp');
    
    await client.close();
  });

  it('应该能够处理房间加入和离开', async () => {
    const client = await createWebSocketClient('ws://localhost:3001');
    
    // 等待连接消息
    await once(client, 'message');
    
    // 加入房间
    const joinMessage = {
      type: 'join_room',
      roomId: 'test-room'
    };
    client.send(JSON.stringify(joinMessage));
    
    // 等待加入房间响应
    const [joinResponseData] = await once(client, 'message');
    const joinResponse = JSON.parse(joinResponseData.toString());
    
    expect(joinResponse.type).toBe('room_joined');
    expect(joinResponse.data.roomId).toBe('test-room');
    
    // 离开房间
    const leaveMessage = {
      type: 'leave_room',
      roomId: 'test-room'
    };
    client.send(JSON.stringify(leaveMessage));
    
    // 等待离开房间响应
    const [leaveResponseData] = await once(client, 'message');
    const leaveResponse = JSON.parse(leaveResponseData.toString());
    
    expect(leaveResponse.type).toBe('room_left');
    expect(leaveResponse.data.roomId).toBe('test-room');
    
    await client.close();
  });

  it('应该能够处理多个客户端连接', async () => {
    const client1 = await createWebSocketClient('ws://localhost:3001');
    const client2 = await createWebSocketClient('ws://localhost:3001');
    
    // 等待两个客户端的连接消息
    await once(client1, 'message');
    await once(client2, 'message');
    
    // 客户端1发送消息
    const message = {
      type: 'chat',
      content: 'Hello from client 1!'
    };
    client1.send(JSON.stringify(message));
    
    // 客户端1应该收到自己的消息（广播）
    const [client1Response] = await once(client1, 'message');
    const client1Data = JSON.parse(client1Response.toString());
    expect(client1Data.type).toBe('chat');
    expect(client1Data.data.content).toBe('Hello from client 1!');
    
    // 客户端2也应该收到消息（广播）
    const [client2Response] = await once(client2, 'message');
    const client2Data = JSON.parse(client2Response.toString());
    expect(client2Data.type).toBe('chat');
    expect(client2Data.data.content).toBe('Hello from client 1!');
    
    await client1.close();
    await client2.close();
  });

  it('应该能够处理无效的JSON消息', async () => {
    const client = await createWebSocketClient('ws://localhost:3001');
    
    // 等待连接消息
    await once(client, 'message');
    
    // 发送无效的JSON
    client.send('invalid json');
    
    // 等待响应
    const [responseData] = await once(client, 'message');
    const response = JSON.parse(responseData.toString());
    
    // 应该收到echo类型的响应
    expect(response.type).toBe('echo');
    expect(response.data.originalMessage).toEqual({ type: 'text', content: 'invalid json' });
    
    await client.close();
  });

  it('应该能够处理自定义消息类型', async () => {
    const client = await createWebSocketClient('ws://localhost:3001');
    
    // 等待连接消息
    await once(client, 'message');
    
    // 发送自定义消息
    const customMessage = {
      type: 'custom_type',
      data: { key: 'value' }
    };
    client.send(JSON.stringify(customMessage));
    
    // 等待响应
    const [responseData] = await once(client, 'message');
    const response = JSON.parse(responseData.toString());
    
    // 应该收到echo类型的响应
    expect(response.type).toBe('echo');
    expect(response.data.originalMessage).toEqual(customMessage);
    
    await client.close();
  });

  it('应该在客户端断开连接时清理资源', async () => {
    const client = await createWebSocketClient('ws://localhost:3001');
    
    // 等待连接消息
    await once(client, 'message');
    
    // 关闭连接
    await client.close();
    
    // 等待一段时间确保服务器处理了断开连接事件
    await sleep(100);
    
    // 验证连接已关闭
    expect(client.readyState).toBe(3); // WebSocket.CLOSED
  });
});
