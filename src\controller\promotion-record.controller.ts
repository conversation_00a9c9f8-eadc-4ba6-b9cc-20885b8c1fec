import {
  Body,
  Controller,
  Del,
  Get,
  Inject,
  Param,
  Post,
  Put,
  Query,
} from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { PromotionRecordService } from '../service/promotion-record.service';
import { CustomError } from '../error/custom.error';
import { Customer } from '../entity';
import { Op } from 'sequelize';

@Controller('/promotion-record')
export class PromotionRecordController {
  @Inject()
  ctx: Context;

  @Inject()
  service: PromotionRecordService;

  @Get('/', { summary: '查询列表' })
  async index(@Query() query: any) {
    let { offset, limit } = query;

    const {
      offset: o,
      limit: l,
      current,
      pageSize,
      sharerOpenName,
      registrantOpenName,
      shareTime,
      registerTime,
      filter,
      ...queryInfo
    } = query;

    void o;
    void l;

    if (current && pageSize) {
      offset = (current - 1) * pageSize;
      limit = pageSize;
    }

    // 处理分享时间查询，忽略时分秒按整天进行匹配
    if (shareTime) {
      const date = new Date(shareTime);
      const startOfDay = new Date(
        date.getFullYear(),
        date.getMonth(),
        date.getDate(),
        0,
        0,
        0,
        0
      );
      const endOfDay = new Date(
        date.getFullYear(),
        date.getMonth(),
        date.getDate(),
        23,
        59,
        59,
        999
      );

      queryInfo.shareTime = {
        [Op.between]: [startOfDay, endOfDay],
      };
    }

    // 处理注册时间查询，忽略时分秒按整天进行匹配
    if (registerTime) {
      const date = new Date(registerTime);
      const startOfDay = new Date(
        date.getFullYear(),
        date.getMonth(),
        date.getDate(),
        0,
        0,
        0,
        0
      );
      const endOfDay = new Date(
        date.getFullYear(),
        date.getMonth(),
        date.getDate(),
        23,
        59,
        59,
        999
      );

      queryInfo.registerTime = {
        [Op.between]: [startOfDay, endOfDay],
      };
    }

    // 构建 include 配置，支持关联表的模糊搜索
    const include = [
      {
        model: Customer,
        as: 'sharer',
        where: sharerOpenName
          ? {
              nickname: {
                [Op.like]: `%${sharerOpenName}%`,
              },
            }
          : undefined,
      },
      {
        model: Customer,
        as: 'registrant',
        where: registrantOpenName
          ? {
              nickname: {
                [Op.like]: `%${registrantOpenName}%`,
              },
            }
          : undefined,
      },
    ];

    return this.service.findAll({
      query: queryInfo,
      offset,
      limit,
      filter,
      include,
    });
  }

  @Get('/:id', { summary: '按ID查询单个' })
  async show(@Param('id') id: string) {
    const res = await this.service.findById(id);
    if (!res) {
      throw new CustomError('未找到指定信息');
    }
    return res;
  }

  @Post('/edit', { summary: '编辑' })
  async edit(@Query() query: any, @Body() body: any) {
    await this.service.update(query, body);
    return true;
  }

  @Post('/', { summary: '新增' })
  async create(@Body() info: any) {
    this.ctx.logger.info('创建推广信息：', info);
    const { sharerUserId, promotionCode, shareTime, registrantUserId } = info;
    if ((!sharerUserId && !promotionCode) || !registrantUserId || !shareTime) {
      throw new CustomError('参数错误');
    }
    let sharerUser: Customer | null = null;
    if (sharerUserId) {
      sharerUser = await Customer.findByPk(sharerUserId);
    }
    if (promotionCode) {
      sharerUser = await Customer.findOne({
        where: { promotionCode },
      });
    }
    if (!sharerUser) {
      throw new CustomError('找不到分享人');
    }

    if (sharerUser.id === registrantUserId) {
      throw new CustomError('分享人不能是自己');
    }
    const registrantUser = await Customer.findByPk(registrantUserId);
    if (!registrantUser) {
      throw new CustomError('找不到被分享人');
    }

    this.ctx.logger.info(
      '条件验证通过，开始创建推广信息',
      JSON.stringify({
        /** 分享人微信openid */
        sharerOpenid: sharerUser.openid,
        /** 分享人系统用户ID */
        sharerUserId: sharerUser.id,
        /** 分享时间 */
        shareTime: new Date(Number(shareTime || 0)),
        /** 注册人微信openid */
        registrantOpenid: registrantUser.openid,
        /** 注册人系统用户ID */
        registrantUserId: registrantUser.id,
        /** 注册时间 */
        registerTime: new Date(),
        /** 分享码 */
        promotionCode: sharerUser.promotionCode,
      })
    );
    const res = await this.service.create({
      /** 分享人微信openid */
      sharerOpenid: sharerUser.openid,
      /** 分享人系统用户ID */
      sharerUserId: sharerUser.id,
      /** 分享时间 */
      shareTime: new Date(Number(shareTime || 0)),
      /** 注册人微信openid */
      registrantOpenid: registrantUser.openid,
      /** 注册人系统用户ID */
      registrantUserId: registrantUser.id,
      /** 注册时间 */
      registerTime: new Date(),
      /** 分享码 */
      promotionCode: sharerUser.promotionCode,
    });
    return res;
  }

  @Put('/:id', { summary: '更新' })
  async update(@Param('id') id: string, @Body() body: any) {
    await this.service.update({ id }, body);
    return true;
  }

  @Del('/:id', { summary: '删除' })
  async destroy(@Param('id') id: string) {
    await this.service.delete({ id });
    return true;
  }

  @Get('/statistics', { summary: '获取分享统计数据' })
  async getStatistics() {
    return await this.service.getStatistics();
  }
}
