import {
  Table,
  Column,
  Model,
  DataType,
  ForeignKey,
  BelongsTo,
  HasMany,
} from 'sequelize-typescript';

export interface AreaAttributes {
  /** 行政区划编码,主键 */
  code: string;
  /** 行政区划名称 */
  name: string;
  /** 父级行政区划编码 */
  parentCode?: string;
}

@Table({ tableName: 'areas', timestamps: false, comment: '行政区划表' })
export class Area extends Model<AreaAttributes> implements AreaAttributes {
  @Column({
    type: DataType.STRING,
    primaryKey: true,
    comment: '行政区划编码',
  })
  code: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
    comment: '行政区划名称',
  })
  name: string;

  @ForeignKey(() => Area)
  @Column({
    type: DataType.STRING,
    allowNull: true,
    comment: '父级行政区划编码',
  })
  parentCode: string;

  @BelongsTo(() => Area, { onDelete: 'CASCADE' })
  parent: Area;

  @HasMany(() => Area, { onDelete: 'CASCADE' })
  children: Area[];
}
