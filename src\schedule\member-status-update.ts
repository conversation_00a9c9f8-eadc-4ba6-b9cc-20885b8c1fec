import { Provide, Inject, Logger } from '@midwayjs/core';
import { ILogger } from '@midwayjs/logger';
import { Job, IJob } from '@midwayjs/cron';
import { CustomerService } from '../service/customer.service';
import { CustomerMembershipCardService } from '../service/customer-membership-card.service';

@Provide()
@Job('memberStatusUpdateJob', {
  cronTime: '0 0 2 * * *', // 每天凌晨2点执行
  start: true, // 应用启动时自动启动任务
})
export class MemberStatusUpdateJob implements IJob {
  @Inject()
  customerService: CustomerService;

  @Inject()
  customerMembershipCardService: CustomerMembershipCardService;

  @Logger()
  logger: ILogger;

  async onTick() {
    this.logger.info('开始执行会员状态更新定时任务...');

    try {
      // 先检查并更新过期的权益卡
      const expiredCount =
        await this.customerMembershipCardService.checkAndUpdateExpiredCards();
      this.logger.info(`已更新 ${expiredCount} 张过期的权益卡`);

      // 更新所有用户的会员状态
      await this.customerService.updateAllMemberStatus();
      this.logger.info('已完成所有用户会员状态的更新');
    } catch (error) {
      this.logger.error('会员状态更新定时任务执行失败:', error);
    }
  }
}
