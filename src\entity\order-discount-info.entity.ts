import {
  Table,
  Column,
  Model,
  DataType,
  BelongsTo,
  ForeignKey,
} from 'sequelize-typescript';
import { Order } from './order.entity';

export enum DiscountType {
  /** 代金券 */
  COUPON = 'coupon',
  /** 权益卡 */
  MEMBERSHIP_CARD = 'membership_card',
}

export interface OrderDiscountInfoAttributes {
  /** 优惠信息ID */
  id: number;
  /** 关联订单ID */
  orderId: number;
  /** 优惠类型：coupon-代金券，membership_card-权益卡 */
  discountType: DiscountType;
  /** 优惠ID：代金券ID或权益卡ID */
  discountId: number;
  /** 优惠金额 */
  discountAmount: number;
  /** 是否已退回 */
  isRefunded: boolean;
  /** 退回时间 */
  refundTime?: Date;
  /** 退回描述 */
  refundDescription?: string;
  /** 关联订单 */
  order?: Order;
}

@Table({
  tableName: 'order_discount_infos',
  timestamps: true,
  comment: '订单优惠信息表',
})
export class OrderDiscountInfo
  extends Model<OrderDiscountInfoAttributes>
  implements OrderDiscountInfoAttributes
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '优惠信息ID',
  })
  id: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '关联订单ID',
  })
  @ForeignKey(() => Order)
  orderId: number;

  @Column({
    type: DataType.ENUM(...Object.values(DiscountType)),
    allowNull: false,
    comment: '优惠类型：coupon-代金券，membership_card-权益卡',
  })
  discountType: DiscountType;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '优惠ID：代金券ID或权益卡ID',
  })
  discountId: number;

  @Column({
    type: DataType.DECIMAL(8, 2),
    allowNull: false,
    comment: '优惠金额',
  })
  discountAmount: number;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
    defaultValue: false,
    comment: '是否已退回',
  })
  isRefunded: boolean;

  @Column({
    type: DataType.DATE,
    allowNull: true,
    comment: '退回时间',
  })
  refundTime?: Date;

  @Column({
    type: DataType.STRING(255),
    allowNull: true,
    comment: '退回描述',
  })
  refundDescription?: string;

  @BelongsTo(() => Order, {
    onDelete: 'CASCADE',
  })
  order?: Order;
}
