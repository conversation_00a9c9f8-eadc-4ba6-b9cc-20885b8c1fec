/*
 * @Description: 数据初始化
 * @Date: 2025-01-06 14:12:26
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-05-02 15:40:11
 */
import { App, Autoload, Init, Inject, Scope, ScopeEnum } from '@midwayjs/core';
import * as koa from '@midwayjs/koa';
import { SystemService } from '../service/system.service';
import { DictionaryCache } from '../common/dictionary-cache';

@Autoload()
@Scope(ScopeEnum.Singleton)
export class AutoloadListener {
  @App('koa')
  app: koa.Application;

  @Inject()
  sysService: SystemService;

  @Inject()
  dictionaryCache: DictionaryCache;

  @Init()
  async init() {
    // 只在主进程初始化
    const env = this.app.getEnv();
    console.log('env: ', env);
    if (
      typeof process.env.NODE_APP_INSTANCE === 'undefined' ||
      process.env.NODE_APP_INSTANCE === '0'
    ) {
      // 检查系统初始化状态
      await this.sysService.checkAndInitSystem();
      // 初始化缓存
      await this.sysService.initCache();
      // 初始化字典缓存，sequelize专用
      await this.dictionaryCache.init();
    }
  }
}
