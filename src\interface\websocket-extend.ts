/**
 * WebSocket 类型扩展
 * 为 WebSocket 和 Context 添加自定义属性的类型定义
 */

import { IClientInfo } from './websocket.interface';

declare module '@midwayjs/ws' {
  interface Context {
    clientInfo?: IClientInfo;
  }
}

declare module 'ws' {
  interface WebSocket {
    clientInfo?: IClientInfo;
  }
}

// 全局类型扩展
declare global {
  namespace WebSocket {
    interface WebSocket {
      clientInfo?: IClientInfo;
    }
  }
}

export {};
