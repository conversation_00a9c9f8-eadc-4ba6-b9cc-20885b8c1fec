import { Provide } from '@midwayjs/core';
import { Coupon } from '../entity/coupon.entity';
import { BaseService } from '../common/BaseService';

@Provide()
export class CouponService extends BaseService<Coupon> {
  constructor() {
    super('代金券');
  }

  getModel() {
    return Coupon;
  }

  async findWithCustomerCoupons() {
    return await this.findAll({
      query: {},
      include: ['userCoupons'],
    });
  }

  /** 按金额查找 */
  async findByAmount(amount: number) {
    return await this.findAll({
      query: { amount },
    });
  }

  /** 按使用门槛查找 */
  async findByThreshold(threshold: number) {
    return await this.findAll({
      query: { threshold },
    });
  }

  /** 更新有效天数 */
  async updateValidDays(id: number, validDays: number) {
    return await this.update({ id }, { validDays });
  }
}
