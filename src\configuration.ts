import { Configuration, App } from '@midwayjs/core';
import * as koa from '@midwayjs/koa';
import * as axios from '@midwayjs/axios';
import * as validate from '@midwayjs/validate';
import * as info from '@midwayjs/info';
import * as cron from '@midwayjs/cron';
import * as ws from '@midwayjs/ws';
import { join } from 'path';
import { DatabaseFilter } from './filter/database.filter';
import { AxiosFilter } from './filter/axios.filter';
import { NotFoundFilter } from './filter/notfound.filter';
import { NoAuthFilter } from './filter/noauth.filter';
import { CustomErrorFilter } from './filter/custom.filter';
import { DefaultErrorFilter } from './filter/default.filter';
import { JWTMiddleware } from './middleware/jwt.middleware';
import { RequestLoggerMiddleware } from './middleware/requestlogger.middleware';
import { FormatMiddleware } from './middleware/format.middleware';
import * as sequelize from '@midwayjs/sequelize';
import * as jwt from '@midwayjs/jwt';
import * as redis from '@midwayjs/redis';
import * as cacheManager from '@midwayjs/cache-manager';

@Configuration({
  imports: [
    koa,
    axios,
    validate,
    sequelize,
    jwt,
    redis,
    cacheManager,
    cron,
    ws,
    {
      component: info,
      enabledEnvironment: ['local'],
    },
  ],
  importConfigs: [join(__dirname, './config')],
})
export class MainConfiguration {
  @App('koa')
  app: koa.Application;

  async onReady() {
    const env = this.app.getEnv();
    // add middleware
    this.app.useMiddleware(
      ['local', 'unittest'].includes(env)
        ? [RequestLoggerMiddleware, JWTMiddleware, FormatMiddleware]
        : [RequestLoggerMiddleware, JWTMiddleware, FormatMiddleware]
    );
    // add filter
    this.app.useFilter([
      NotFoundFilter,
      NoAuthFilter,
      DatabaseFilter,
      AxiosFilter,
      CustomErrorFilter,
      DefaultErrorFilter,
    ]);
  }
}
