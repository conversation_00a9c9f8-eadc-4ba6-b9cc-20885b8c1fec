import { Provide } from '@midwayjs/core';
import { Banner } from '../entity/banner.entity';
import { BaseService } from '../common/BaseService';

@Provide()
export class BannerService extends BaseService<Banner> {
  constructor() {
    super('轮播图');
  }

  getModel() {
    return Banner;
  }

  async findEnabled() {
    return await this.findAll({
      query: {},
      order: [['priority', 'DESC']],
    });
  }

  async updatePriority(id: number, priority: number) {
    return await this.update({ id }, { priority });
  }
}
