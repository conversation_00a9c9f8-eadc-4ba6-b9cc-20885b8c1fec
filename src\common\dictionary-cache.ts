/*
 * @Description: 因为sequelize中需要使用缓存，midway的缓存不支持依赖注入，所以只能使用单例模式
 * @Date: 2025-05-02 15:11:27
 * @LastEditors: ZhuPengliang <EMAIL>
 * @LastEditTime: 2025-05-03 09:02:47
 */
import { Provide, Singleton, Init } from '@midwayjs/core';
import { Dictionary, DictionaryAttributes } from '../entity';

@Provide()
@Singleton()
export class DictionaryCache {
  private static instance: DictionaryCache;
  private localCache: Map<string, DictionaryAttributes> = new Map();

  @Init()
  async init() {
    DictionaryCache.instance = this;
    await this.loadAllDictionaries();
  }

  public static getInstance(): DictionaryCache {
    return DictionaryCache.instance;
  }

  // 同步方法，从本地内存获取
  getSync(code: string): DictionaryAttributes | null {
    return this.localCache.get(code) || null;
  }

  // 预加载所有字典到内存
  private async loadAllDictionaries() {
    try {
      // 从数据库加载所有字典
      const dictionaries = await Dictionary.findAll();

      // 存入本地缓存
      dictionaries.forEach(dict => {
        this.localCache.set(dict.code, dict.toJSON());
      });

      console.log(`已加载 ${this.localCache.size} 个字典到本地缓存`);
    } catch (error) {
      console.error('加载字典到本地缓存失败:', error);
    }
  }

  // 刷新本地缓存
  async refreshLocalCache() {
    await this.loadAllDictionaries();
  }
}
