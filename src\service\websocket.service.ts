import { Provide, App, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>opeEnum } from '@midwayjs/core';
import { Application } from '@midwayjs/ws';
import { ILogger } from '@midwayjs/logger';

/**
 * 简单的 WebSocket 服务类
 */
@Provide()
@Scope(ScopeEnum.Singleton)
export class WebSocketService {
  @App('webSocket')
  wsApp: Application;

  @Logger()
  logger: ILogger;

  /**
   * 向所有连接的客户端广播消息
   */
  async broadcastToAll(message: any) {
    const messageStr = JSON.stringify(message);
    let clientCount = 0;

    this.wsApp.clients.forEach(client => {
      if (client.readyState === 1) { // WebSocket.OPEN
        client.send(messageStr);
        clientCount++;
      }
    });

    this.logger.info(`广播消息给 ${clientCount} 个客户端`);
    return clientCount;
  }

  /**
   * 获取当前连接的客户端数量
   */
  getConnectedClientCount(): number {
    let count = 0;
    this.wsApp.clients.forEach(client => {
      if (client.readyState === 1) {
        count++;
      }
    });
    return count;
  }
}
