import { Provide, App, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>opeEnum } from '@midwayjs/core';
import { Application, Context } from '@midwayjs/ws';
import { ILogger } from '@midwayjs/logger';
import '../interface/websocket-extend';

/**
 * WebSocket 服务类
 * 提供 WebSocket 相关的业务逻辑和工具方法
 */
@Provide()
@Scope(ScopeEnum.Singleton)
export class WebSocketService {
  @App('webSocket')
  wsApp: Application;

  @Logger()
  logger: ILogger;

  /**
   * 向所有连接的客户端广播消息
   * @param message 要广播的消息
   */
  async broadcastToAll(message: any) {
    const messageStr = JSON.stringify(message);
    let clientCount = 0;

    this.wsApp.clients.forEach(client => {
      if (client.readyState === 1) { // WebSocket.OPEN
        client.send(messageStr);
        clientCount++;
      }
    });

    this.logger.info(`向 ${clientCount} 个客户端广播消息:`, message);
    return clientCount;
  }

  /**
   * 向除了指定客户端外的所有客户端广播消息
   * @param excludeClient 要排除的客户端
   * @param message 要广播的消息
   */
  async broadcastToOthers(excludeClient: Context, message: any) {
    const messageStr = JSON.stringify(message);
    let clientCount = 0;

    this.wsApp.clients.forEach(client => {
      if (client !== excludeClient && client.readyState === 1) {
        client.send(messageStr);
        clientCount++;
      }
    });

    this.logger.info(`向 ${clientCount} 个其他客户端广播消息:`, message);
    return clientCount;
  }

  /**
   * 向指定客户端发送消息
   * @param clientId 客户端ID
   * @param message 要发送的消息
   */
  async sendToClient(clientId: string, message: any) {
    const messageStr = JSON.stringify(message);
    let sent = false;

    this.wsApp.clients.forEach(client => {
      const extendedClient = client as any;
      if (extendedClient.clientInfo?.id === clientId && client.readyState === 1) {
        client.send(messageStr);
        sent = true;
      }
    });

    if (sent) {
      this.logger.info(`向客户端 ${clientId} 发送消息:`, message);
    } else {
      this.logger.warn(`客户端 ${clientId} 未找到或未连接`);
    }

    return sent;
  }

  /**
   * 获取当前连接的客户端数量
   */
  getConnectedClientCount(): number {
    let count = 0;
    this.wsApp.clients.forEach(client => {
      if (client.readyState === 1) {
        count++;
      }
    });
    return count;
  }

  /**
   * 获取所有连接的客户端信息
   */
  getConnectedClients(): any[] {
    const clients = [];
    this.wsApp.clients.forEach(client => {
      const extendedClient = client as any;
      if (client.readyState === 1 && extendedClient.clientInfo) {
        clients.push({
          id: extendedClient.clientInfo.id,
          ip: extendedClient.clientInfo.ip,
          userAgent: extendedClient.clientInfo.userAgent,
          connectedAt: extendedClient.clientInfo.connectedAt,
        });
      }
    });
    return clients;
  }

  /**
   * 向房间内的所有客户端发送消息
   * @param roomId 房间ID
   * @param message 要发送的消息
   */
  async sendToRoom(roomId: string, message: any) {
    const messageStr = JSON.stringify(message);
    let clientCount = 0;

    this.wsApp.clients.forEach(client => {
      const extendedClient = client as any;
      if (
        client.readyState === 1 &&
        extendedClient.clientInfo?.rooms?.includes(roomId)
      ) {
        client.send(messageStr);
        clientCount++;
      }
    });

    this.logger.info(`向房间 ${roomId} 的 ${clientCount} 个客户端发送消息:`, message);
    return clientCount;
  }

  /**
   * 将客户端加入房间
   * @param clientId 客户端ID
   * @param roomId 房间ID
   */
  async joinRoom(clientId: string, roomId: string): Promise<boolean> {
    let joined = false;

    this.wsApp.clients.forEach(client => {
      const extendedClient = client as any;
      if (extendedClient.clientInfo?.id === clientId && client.readyState === 1) {
        if (!extendedClient.clientInfo.rooms) {
          extendedClient.clientInfo.rooms = [];
        }
        if (!extendedClient.clientInfo.rooms.includes(roomId)) {
          extendedClient.clientInfo.rooms.push(roomId);
          joined = true;
        }
      }
    });

    if (joined) {
      this.logger.info(`客户端 ${clientId} 加入房间 ${roomId}`);
    }

    return joined;
  }

  /**
   * 将客户端从房间移除
   * @param clientId 客户端ID
   * @param roomId 房间ID
   */
  async leaveRoom(clientId: string, roomId: string): Promise<boolean> {
    let left = false;

    this.wsApp.clients.forEach(client => {
      const extendedClient = client as any;
      if (extendedClient.clientInfo?.id === clientId && client.readyState === 1) {
        if (extendedClient.clientInfo.rooms) {
          const index = extendedClient.clientInfo.rooms.indexOf(roomId);
          if (index > -1) {
            extendedClient.clientInfo.rooms.splice(index, 1);
            left = true;
          }
        }
      }
    });

    if (left) {
      this.logger.info(`客户端 ${clientId} 离开房间 ${roomId}`);
    }

    return left;
  }

  /**
   * 获取房间内的客户端列表
   * @param roomId 房间ID
   */
  getRoomClients(roomId: string): any[] {
    const clients = [];
    this.wsApp.clients.forEach(client => {
      const extendedClient = client as any;
      if (
        client.readyState === 1 &&
        extendedClient.clientInfo?.rooms?.includes(roomId)
      ) {
        clients.push({
          id: extendedClient.clientInfo.id,
          ip: extendedClient.clientInfo.ip,
          connectedAt: extendedClient.clientInfo.connectedAt,
        });
      }
    });
    return clients;
  }

  /**
   * 发送系统通知
   * @param message 通知消息
   * @param targetClientId 目标客户端ID，如果不指定则广播给所有客户端
   */
  async sendSystemNotification(message: string, targetClientId?: string) {
    const notification = {
      type: 'system_notification',
      data: {
        message,
        timestamp: new Date().toISOString(),
      },
    };

    if (targetClientId) {
      return await this.sendToClient(targetClientId, notification);
    } else {
      return await this.broadcastToAll(notification);
    }
  }

  /**
   * 关闭指定客户端连接
   * @param clientId 客户端ID
   * @param reason 关闭原因
   */
  async disconnectClient(clientId: string, reason?: string) {
    let disconnected = false;

    this.wsApp.clients.forEach(client => {
      const extendedClient = client as any;
      if (extendedClient.clientInfo?.id === clientId && client.readyState === 1) {
        if (reason) {
          client.send(
            JSON.stringify({
              type: 'disconnect',
              data: {
                reason,
                timestamp: new Date().toISOString(),
              },
            })
          );
        }
        client.terminate();
        disconnected = true;
      }
    });

    if (disconnected) {
      this.logger.info(`强制断开客户端 ${clientId} 连接, 原因: ${reason || '未指定'}`);
    }

    return disconnected;
  }
}
