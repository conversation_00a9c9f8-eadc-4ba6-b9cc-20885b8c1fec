import {
  Body,
  Controller,
  Del,
  Get,
  Inject,
  Param,
  Post,
  Put,
  Query,
} from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { RoleService } from '../service/role.service';
import { CustomError } from '../error/custom.error';
import { Op } from 'sequelize';
import { Feature, Permission } from '../entity';

@Controller('/roles')
export class RoleController {
  @Inject()
  ctx: Context;

  @Inject()
  service: RoleService;

  @Get('/', { summary: '查询角色列表' })
  async index(@Query() query: any) {
    let { offset, limit } = query;
    const { offset: o, limit: l, current, pageSize, ...queryInfo } = query;
    void o;
    void l;

    if (current && pageSize) {
      offset = (current - 1) * pageSize;
      limit = pageSize;
    }
    // name支持模糊查询
    if (queryInfo.name) {
      queryInfo.name = {
        [Op.like]: `%${queryInfo.name}%`,
      };
    }
    return this.service.findAll({
      query: queryInfo,
      offset,
      limit,
      include: [
        {
          model: Permission,
          include: [{ model: Feature }],
        },
      ],
    });
  }

  @Get('/:id', { summary: '按ID查询角色' })
  async show(@Param('id') id: number) {
    const res = await this.service.findById(id);
    if (!res) {
      throw new CustomError('未找到指定角色');
    }
    return res;
  }

  @Post('/', { summary: '新增角色' })
  async create(@Body() info: any) {
    const res = await this.service.create(info);
    return res;
  }

  @Put('/:id', { summary: '更新角色' })
  async update(@Param('id') id: number, @Body() body: any) {
    await this.service.update({ id }, body);
    return true;
  }

  @Del('/:id', { summary: '删除角色' })
  async destroy(@Param('id') id: number) {
    await this.service.delete({ id });
    return true;
  }

  @Post('/:id/permissions', { summary: '为角色添加权限' })
  async addPermissions(
    @Param('id') id: number,
    @Body() body: { permissionIds: number[] }
  ) {
    await this.service.addPermissions(id, body.permissionIds);
    return true;
  }

  @Del('/:id/permissions', { summary: '为角色删除权限' })
  async delPermissions(
    @Param('id') id: number,
    @Body() body: { permissionIds: number[] }
  ) {
    await this.service.delPermissions(id, body.permissionIds);
    return true;
  }

  @Get('/:id/users', { summary: '查询角色下所有用户' })
  async getUsers(@Param('id') id: number, @Query() query: any) {
    const { offset, limit, order, current, pageSize, ...queryInfo } = query;

    let currentOffset = parseInt(offset, 10) || 0;
    let currentLimit = parseInt(limit, 10) || 20; // 默认值为20
    if (current && pageSize) {
      currentOffset = (parseInt(current, 10) - 1) * parseInt(pageSize, 10);
      currentLimit = parseInt(pageSize, 10);
    }

    // username支持模糊查询
    if (queryInfo.username) {
      queryInfo.username = {
        [Op.like]: `%${queryInfo.username}%`,
      };
    }

    const options = {
      where: queryInfo,
      offset: currentOffset,
      limit: currentLimit,
      order: order ? [order.split(',')] : undefined,
    };

    return this.service.getUsers(id, options);
  }

  @Post('/:id/users', { summary: '为角色增加用户' })
  async addUsers(@Param('id') id: number, @Body() body: { userIds: number[] }) {
    await this.service.addUsers(id, body.userIds);
    return true;
  }

  @Del('/:id/users', { summary: '为角色删除用户' })
  async delUsers(@Param('id') id: number, @Body() body: { userIds: number[] }) {
    await this.service.delUsers(id, body.userIds);
    return true;
  }
}
