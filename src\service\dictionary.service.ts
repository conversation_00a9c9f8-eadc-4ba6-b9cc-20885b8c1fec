import { Provide } from '@midwayjs/core';
import { Dictionary } from '../entity/dictionary.entity';
import { BaseService } from '../common/BaseService';

@Provide()
export class DictionaryService extends BaseService<Dictionary> {
  constructor() {
    super('字典');
  }

  getModel() {
    return Dictionary;
  }

  /**
   * 自动计算下一个排序值
   *
   * @param {string} type 字典类型
   * @return {*} res
   * @memberof DictionaryService
   */
  async getNextOrder(type: string) {
    const sortOrder = await Dictionary.max('sortOrder', { where: { type } });
    return sortOrder ? (sortOrder as number) + 1 : 1;
  }
}
