import { Body, Controller, Inject, Post } from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { WeappService } from '../service/weapp.service';

@Controller('/subscription')
export class SubscriptionController {
  @Inject()
  ctx: Context;

  @Inject()
  service: WeappService;

  @Post('/', { summary: '创建订阅信息' })
  async index(
    @Body()
    body: {
      openId: string;
      templateId: string;
      orderId: string;
      status: boolean;
    }
  ) {
    const { openId, templateId, orderId, status } = body;

    return this.service.saveSubscription(openId, templateId, orderId, status);
  }
}
