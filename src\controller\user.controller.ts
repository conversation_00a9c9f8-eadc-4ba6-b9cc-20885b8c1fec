import {
  Body,
  Controller,
  Del,
  Get,
  Inject,
  Param,
  Post,
  Put,
  Query,
} from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { UserService } from '../service/user.service';
import { CosService } from '../service/cos.service';
import { CustomError } from '../error/custom.error';
import { Op } from 'sequelize';
import { Role } from '../entity';

@Controller('/users')
export class UserController {
  @Inject()
  ctx: Context;

  @Inject()
  service: UserService;

  @Inject()
  cosService: CosService;

  @Get('/', { summary: '查询用户列表' })
  async index(@Query() query: any) {
    let { offset, limit } = query;
    const {
      offset: o,
      limit: l,
      current,
      pageSize,
      sort,
      filter,
      role,
      ...queryInfo
    } = query;
    void o;
    void l;

    if (current && pageSize) {
      offset = (current - 1) * pageSize;
      limit = pageSize;
    }
    // username支持模糊查询
    if (queryInfo.username) {
      queryInfo.username = {
        [Op.like]: `%${queryInfo.username}%`,
      };
    }

    return this.service.findAll({
      query: queryInfo,
      offset,
      limit,
      sort,
      filter,
      attributes: { exclude: ['password'] },
      include: [{ model: Role, where: role ? { id: role } : undefined }],
    });
  }

  @Get('/:id', { summary: '按ID查询用户' })
  async show(@Param('id') id: string) {
    const res = await this.service.findById(id);
    if (!res) {
      throw new CustomError('未找到指定用户');
    }
    return res;
  }

  @Post('/', { summary: '新增用户' })
  async create(@Body() info: any) {
    const res = await this.service.create(info);
    return res;
  }

  @Put('/:id', { summary: '更新用户' })
  async update(@Param('id') id: string, @Body() body: any) {
    await this.service.update({ id }, body);
    return true;
  }

  @Del('/:id', { summary: '删除用户' })
  async destroy(@Param('id') id: string) {
    const user = await this.service.findById(id);
    const { avatar } = user || {};
    if (avatar) {
      const key = avatar?.split('myqcloud.com/')[1];
      this.cosService.deleteObject(key);
    }
    await this.service.delete({ id });
    return true;
  }

  @Get('/count', { summary: '查询用户数量' })
  async count() {
    return this.service.count();
  }
}
