import {
  Body,
  Controller,
  Del,
  Get,
  Inject,
  Param,
  Post,
  Put,
  Query,
} from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { AreaService } from '../service/area.service';
import { CustomError } from '../error/custom.error';
import { Op } from 'sequelize';

@Controller('/areas')
export class AreaController {
  @Inject()
  ctx: Context;

  @Inject()
  service: AreaService;

  @Get('/', { summary: '查询行政区划列表' })
  async index(@Query() query: any) {
    let { offset, limit } = query;
    const { offset: o, limit: l, current, pageSize, ...queryInfo } = query;
    void o;
    void l;

    if (current && pageSize) {
      offset = (current - 1) * pageSize;
      limit = pageSize;
    }
    // areaName支持模糊查询
    if (queryInfo.name) {
      queryInfo.name = {
        [Op.like]: `%${queryInfo.name}%`,
      };
    }
    return this.service.findAll({
      query: queryInfo,
      offset,
      limit,
      order: [['code', 'ASC']],
    });
  }

  @Get('/:code', { summary: '按ID查询行政区划' })
  async show(@Param('id') id: number) {
    const res = await this.service.findById(id);
    if (!res) {
      throw new CustomError('未找到指定行政区划');
    }
    return res;
  }

  @Post('/', { summary: '新增行政区划' })
  async create(@Body() info: any) {
    const res = await this.service.create(info);
    return res;
  }

  @Put('/:code', { summary: '更新行政区划' })
  async update(@Param('code') code: number, @Body() body: any) {
    await this.service.update({ code }, body);
    return true;
  }

  @Del('/:code', { summary: '删除行政区划' })
  async destroy(@Param('code') code: number) {
    await this.service.delete({ code });
    return true;
  }
}
