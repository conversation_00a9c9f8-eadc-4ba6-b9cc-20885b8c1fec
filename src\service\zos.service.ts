import { Provide, Inject, Config } from '@midwayjs/core';
import { ZosSigner } from '../common/ZosSigner';
import { HttpService } from '@midwayjs/axios';
import * as https from 'https';

@Provide()
export class ZOSService {
  @Config('zos')
  config: {
    baseURL: string;
    bucket: string;
    regionID: string;
    ak: string;
    sk: string;
  };

  @Inject()
  zosSigner: ZosSigner;

  @Inject()
  httpService: HttpService;

  /**
   * 发送请求，统一使用原生的 https 模块
   * @param method 请求方法
   * @param urlStr 请求地址
   * @param query 请求参数
   * @param body 请求体
   * @returns Promise<Response>
   */
  private async request(
    method: string,
    urlStr: string,
    query?: Record<string, string | number | boolean>,
    body?: any
  ): Promise<any> {
    const url = new URL(this.config.baseURL + urlStr);
    if (query) {
      Object.keys(query).forEach(key => {
        url.searchParams.set(key, query[key] + '');
      });
    }
    const headers = this.zosSigner.signRequest({
      url,
      headers: new Headers(),
      body,
    });

    // 使用 Node.js 原生的 https 模块发送请求
    return new Promise((resolve, reject) => {
      const options = {
        method: method || 'GET',
        headers: {
          'Eop-date': headers['Eop-date'],
          'ctyun-eop-request-id': headers['ctyun-eop-request-id'],
          'Eop-Authorization': headers['Eop-Authorization'],
        },
      };

      let postData;
      if (method.toLowerCase() === 'post' && body) {
        postData = JSON.stringify(body);
        // options.headers['Content-Type'] = 'application/json';
        // options.headers['Content-Length'] = Buffer.byteLength(postData);
      }
      const req = https.request(url, options, res => {
        let data = '';
        res.on('data', chunk => {
          data += chunk;
        });
        res.on('end', () => {
          try {
            if (res.statusCode !== 200) {
              console.error('请求失败，状态码：', res.statusCode);
              console.error('请求对象：', options);
              console.error('响应头：', res.headers);
              console.error('响应数据：', data);
              resolve({});
              return;
            }
            const responseData = JSON.parse(data);
            if (responseData.errorCode) {
              console.log('请求失败，错误码：', responseData.errorCode);
              console.dir(responseData);
            }
            resolve(responseData);
          } catch (e) {
            console.error('解析响应数据失败:', e);
            resolve({});
          }
        });
      });

      if (method.toLowerCase() === 'post' && body) {
        // 写入请求参数
        req.write(postData);
      }

      req.on('error', error => {
        console.error('请求失败:', error.message);
        reject(error);
      });

      req.end();
    });
  }

  async getRegions() {
    const res = await this.request('GET', '/v4/oss/list-objects', {
      bucket: this.config.bucket,
      regionID: this.config.regionID,
    });
    return res;
  }

  // 生成对象上传链接
  async getObjectUploadLink(key: string) {
    return this.request('POST', '/v4/oss/generate-object-upload-link', null, {
      bucket: this.config.bucket,
      regionID: this.config.regionID,
      key,
    });
  }

  // 设置对象的 HTTP 头
  async setObjectHeaders(key: string, ContentType: string) {
    const data = {
      bucket: this.config.bucket,
      regionID: this.config.regionID,
      key: key,
      headers: {
        ContentType,
      },
    };
    return this.request('POST', '/v4/oss/put-object-header', null, data);
  }

  // 设置对象ACL
  async setObjectACL(
    key: string,
    ACL: 'private' | 'public-read' | 'public-read-write' | 'authenticated-read'
  ) {
    const data = {
      bucket: this.config.bucket,
      regionID: this.config.regionID,
      key: key,
      ACL,
    };
    return this.request('POST', '/v4/oss/put-object-acl', null, data);
  }
}
