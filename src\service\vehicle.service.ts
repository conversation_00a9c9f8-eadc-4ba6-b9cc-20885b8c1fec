import { Provide } from '@midwayjs/core';
import { Vehicle } from '../entity/vehicle.entity';
import { BaseService } from '../common/BaseService';
import { Employee } from '../entity/employee.entity';

@Provide()
export class VehicleService extends BaseService<Vehicle> {
  constructor() {
    super('车辆');
  }

  getModel() {
    return Vehicle;
  }

  async findByPlateNumber(plateNumber: string) {
    return await this.findOne({ where: { plateNumber } });
  }

  async updateLocation(id: number, latitude: number, longitude: number) {
    return await this.update({ id }, { latitude, longitude });
  }

  async updateStatus(id: number, status: string) {
    return await this.update({ id }, { status });
  }

  async getEmployee(vehicleId: number) {
    const vehicle = await Vehicle.findByPk(vehicleId, {
      include: [Employee],
    });
    return vehicle?.employee;
  }
}
