import {
  Body,
  Controller,
  Del,
  Get,
  Inject,
  Param,
  Post,
  Put,
  Query,
} from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { ProductService } from '../service/product.service';
import { CustomError } from '../error/custom.error';
import { Op } from 'sequelize';
import { ProductType } from '../entity';

@Controller('/products')
export class ProductController {
  @Inject()
  ctx: Context;

  @Inject()
  service: ProductService;

  @Get('/', { summary: '查询商品列表' })
  async index(@Query() query: any) {
    let { offset, limit } = query;
    const { offset: o, limit: l, current, pageSize, ...queryInfo } = query;
    void o;
    void l;

    if (current && pageSize) {
      offset = (current - 1) * pageSize;
      limit = pageSize;
    }
    // name支持模糊查询
    if (queryInfo.name) {
      queryInfo.name = {
        [Op.like]: `%${queryInfo.name}%`,
      };
    }
    return this.service.findAll({
      query: queryInfo,
      offset,
      limit,
      order: [['createdAt', 'DESC']],
      include: [
        {
          model: ProductType,
        },
      ],
    });
  }

  @Get('/:id', { summary: '按ID查询商品' })
  async show(@Param('id') id: number) {
    const res = await this.service.findById(id);
    if (!res) {
      throw new CustomError('未找到指定商品');
    }
    return res;
  }

  @Post('/', { summary: '新增商品' })
  async create(@Body() info: any) {
    const res = await this.service.create(info);
    return res;
  }

  @Put('/:id', { summary: '更新商品' })
  async update(@Param('id') id: number, @Body() body: any) {
    await this.service.update({ id }, body);
    return true;
  }

  @Del('/:id', { summary: '删除商品' })
  async destroy(@Param('id') id: number) {
    await this.service.delete({ id });
    return true;
  }
}
