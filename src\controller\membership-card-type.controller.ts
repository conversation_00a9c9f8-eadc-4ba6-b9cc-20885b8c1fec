import {
  Body,
  Controller,
  Del,
  Get,
  Inject,
  Param,
  Post,
  Put,
  Query,
} from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { MembershipCardTypeService } from '../service/membership-card-type.service';
import { CustomError } from '../error/custom.error';
import { Op, WhereOptions } from 'sequelize';
import { QueryMembershipCardTypeDto } from '../dto/membership-card-type.dto';
import { MembershipCardTypeAttributes } from '../entity';

@Controller('/membership-card-types')
export class MembershipCardTypeController {
  @Inject()
  ctx: Context;

  @Inject()
  service: MembershipCardTypeService;

  @Get('/', { summary: '查询会员卡类型列表' })
  async index(@Query() query: QueryMembershipCardTypeDto) {
    let { offset, limit } = query;
    const { offset: o, limit: l, current, pageSize, ...queryInfo } = query;
    void o;
    void l;

    const queryOpt: WhereOptions<MembershipCardTypeAttributes> = queryInfo;

    if (current && pageSize) {
      offset = (current - 1) * pageSize;
      limit = pageSize;
    }
    // name支持模糊查询
    if (queryOpt.name) {
      queryOpt.name = {
        [Op.like]: `%${queryInfo.name}%`,
      };
    }
    return this.service.findAll({
      query: queryInfo,
      offset,
      limit,
      order: [['createdAt', 'ASC']],
    });
  }

  @Get('/by-type/:type', { summary: '按类型查询会员卡类型列表' })
  async getByType(@Param('type') type: 'discount' | 'times') {
    if (type !== 'discount' && type !== 'times') {
      throw new CustomError('权益卡类型必须是折扣卡(discount)或次卡(times)');
    }
    return this.service.findByType(type);
  }

  @Get('/:id', { summary: '按ID查询会员卡类型' })
  async show(@Param('id') id: number) {
    const res = await this.service.findById(id);
    if (!res) {
      throw new CustomError('未找到指定会员卡类型');
    }
    return res;
  }

  @Post('/', { summary: '新增会员卡类型' })
  async create(@Body() info: any) {
    // 验证折扣卡和次卡的特殊规则
    this.validateCardTypeRules(info);
    const res = await this.service.create(info);
    return res;
  }

  @Put('/:id', { summary: '更新会员卡类型' })
  async update(@Param('id') id: number, @Body() body: any) {
    // 如果更新包含type字段，需要验证折扣卡和次卡的特殊规则
    if (body.type) {
      // 获取当前卡信息，与更新内容合并后验证
      const currentCard = await this.service.findById(id);
      if (!currentCard) {
        throw new CustomError('未找到指定会员卡类型');
      }

      const mergedData = { ...currentCard.toJSON(), ...body };
      this.validateCardTypeRules(mergedData);
    }

    await this.service.update({ id }, body);
    return true;
  }

  /**
   * 验证权益卡类型的特殊规则
   * @param data 权益卡数据
   */
  private validateCardTypeRules(data: any) {
    if (data.type === 'discount') {
      // 折扣卡必须设置折扣率
      if (data.discountRate === undefined || data.discountRate === null) {
        throw new CustomError('折扣卡必须设置折扣率');
      }
    } else if (data.type === 'times') {
      // 次卡必须设置使用次数
      if (data.usageLimit === undefined || data.usageLimit === null) {
        throw new CustomError('次卡必须设置使用次数');
      }

      // 次卡折扣率强制为0
      data.discountRate = 0;
    }
  }

  @Del('/:id', { summary: '删除会员卡类型' })
  async destroy(@Param('id') id: number) {
    await this.service.delete({ id });
    return true;
  }
}
