import {
  Table,
  Column,
  Model,
  DataType,
  HasMany,
  BeforeUpdate,
  BeforeCreate,
} from 'sequelize-typescript';
import { CustomerMembershipCard } from './customer-membership-card.entity';
import { ApplicableScope } from '../common/Constant';

export interface MembershipCardTypeAttributes {
  /** 卡类型唯一标识 */
  id: number;
  /** 名称 */
  name: string;
  /** 售价 */
  price: number;
  /** 权益卡类型，折扣卡或次卡 */
  type: 'discount' | 'times';
  /** 有效期天数 */
  validDays?: number;
  /** 折扣率 */
  discountRate?: number;
  /** 可用次数 */
  usageLimit?: number;
  /** 适用范围 */
  applicableScope: ApplicableScope;
  /** 适用服务类型名称列表，适用范围为指定服务类型时必填 */
  applicableServiceTypes?: string[];
  /** 适用服务品牌ID列表，适用范围为指定服务品牌时必填 */
  applicableServiceCategories?: number[];
  /** 适用服务ID列表，适用范围为指定服务时必填 */
  applicableServices?: number[];
  /** 适用商品类别ID列表，适用范围为指定商品类别时必填 */
  applicableProductTypes?: number[];
  /** 适用商品ID列表，适用范围为指定商品时必填 */
  applicableProducts?: number[];
  /** 权益描述 */
  description?: string;
  /** 是否启用 */
  isEnabled: boolean;
  /** 关联的用户权益卡信息 */
  userMembershipCards?: CustomerMembershipCard[];
}

@Table({
  tableName: 'membership_card_types',
  timestamps: true,
  comment: '权益卡类型表',
})
export class MembershipCardType
  extends Model<MembershipCardTypeAttributes>
  implements MembershipCardTypeAttributes
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '卡类型唯一标识',
  })
  id: number;

  @Column({
    type: DataType.STRING(50),
    allowNull: false,
    comment: '名称',
  })
  name: string;

  @Column({
    type: DataType.DECIMAL(8, 2),
    allowNull: false,
    comment: '售价',
  })
  price: number;

  @Column({
    type: DataType.ENUM('discount', 'times'),
    allowNull: false,
    comment: '权益卡类型，折扣卡或次卡',
  })
  type: 'discount' | 'times';

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    comment: '有效期天数',
  })
  validDays?: number;

  @Column({
    type: DataType.FLOAT,
    comment: '折扣率',
  })
  discountRate?: number;

  @Column({
    type: DataType.INTEGER,
    comment: '可用次数',
  })
  usageLimit?: number;

  @Column({
    type: DataType.ENUM(...Object.values(ApplicableScope)),
    allowNull: false,
    comment: '适用范围',
  })
  applicableScope: ApplicableScope;

  @Column({
    type: DataType.JSON,
    comment: '适用服务类型名称列表，适用范围为指定服务类型时必填',
  })
  applicableServiceTypes?: string[];

  @Column({
    type: DataType.JSON,
    comment: '适用服务品牌ID列表，适用范围为指定服务品牌时必填',
  })
  applicableServiceCategories?: number[];

  @Column({
    type: DataType.JSON,
    comment: '适用服务ID列表，适用范围为指定服务时必填',
  })
  applicableServices?: number[];

  @Column({
    type: DataType.JSON,
    comment: '适用商品类别ID列表，适用范围为指定商品类别时必填',
  })
  applicableProductTypes?: number[];

  @Column({
    type: DataType.JSON,
    comment: '适用商品ID列表，适用范围为指定商品时必填',
  })
  applicableProducts?: number[];

  @Column({
    type: DataType.TEXT,
    comment: '权益描述',
  })
  description?: string;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
    defaultValue: true,
    comment: '是否启用',
  })
  isEnabled: boolean;

  @HasMany(() => CustomerMembershipCard)
  userMembershipCards?: CustomerMembershipCard[];

  @BeforeCreate
  static async hashPassword(instance: MembershipCardType) {
    if (instance.usageLimit < 0) {
      instance.usageLimit = null;
    }

    // 根据卡类型设置相应的属性
    if (instance.type === 'discount') {
      // 折扣卡必须设置折扣率
      if (
        instance.discountRate === undefined ||
        instance.discountRate === null
      ) {
        throw new Error('折扣卡必须设置折扣率');
      }
    } else if (instance.type === 'times') {
      // 次卡必须设置使用次数
      if (instance.usageLimit === undefined || instance.usageLimit === null) {
        throw new Error('次卡必须设置使用次数');
      }
      // 次卡折扣率强制为0
      instance.discountRate = 0;
    }

    // 暂时只不支持混合选择
    switch (instance.applicableScope) {
      case ApplicableScope.不限:
      case ApplicableScope.所有服务:
      case ApplicableScope.所有商品:
        /** 适用服务类型名称列表，适用范围为指定服务类型时必填 */
        instance.applicableServiceTypes = null;
        /** 适用服务品牌ID列表，适用范围为指定服务品牌时必填 */
        instance.applicableServiceCategories = null;
        /** 适用服务ID列表，适用范围为指定服务时必填 */
        instance.applicableServices = null;
        /** 适用商品类别ID列表，适用范围为指定商品类别时必填 */
        instance.applicableProductTypes = null;
        /** 适用商品ID列表，适用范围为指定商品时必填 */
        instance.applicableProducts = null;
        break;
      case ApplicableScope.指定服务类别:
        /** 适用服务品牌ID列表，适用范围为指定服务品牌时必填 */
        instance.applicableServiceCategories = null;
        /** 适用服务ID列表，适用范围为指定服务时必填 */
        instance.applicableServices = null;
        /** 适用商品类别ID列表，适用范围为指定商品类别时必填 */
        instance.applicableProductTypes = null;
        /** 适用商品ID列表，适用范围为指定商品时必填 */
        instance.applicableProducts = null;
        break;
      case ApplicableScope.指定服务品牌:
        /** 适用服务类型名称列表，适用范围为指定服务类型时必填 */
        instance.applicableServiceTypes = null;
        /** 适用服务ID列表，适用范围为指定服务时必填 */
        instance.applicableServices = null;
        /** 适用商品类别ID列表，适用范围为指定商品类别时必填 */
        instance.applicableProductTypes = null;
        /** 适用商品ID列表，适用范围为指定商品时必填 */
        instance.applicableProducts = null;
        break;
      case ApplicableScope.指定服务:
        /** 适用服务类型名称列表，适用范围为指定服务类型时必填 */
        instance.applicableServiceTypes = null;
        /** 适用服务品牌ID列表，适用范围为指定服务品牌时必填 */
        instance.applicableServiceCategories = null;
        /** 适用商品类别ID列表，适用范围为指定商品类别时必填 */
        instance.applicableProductTypes = null;
        /** 适用商品ID列表，适用范围为指定商品时必填 */
        instance.applicableProducts = null;
        break;
      case ApplicableScope.指定商品类别:
        /** 适用服务类型名称列表，适用范围为指定服务类型时必填 */
        instance.applicableServiceTypes = null;
        /** 适用服务品牌ID列表，适用范围为指定服务品牌时必填 */
        instance.applicableServiceCategories = null;
        /** 适用服务ID列表，适用范围为指定服务时必填 */
        instance.applicableServices = null;
        /** 适用商品ID列表，适用范围为指定商品时必填 */
        instance.applicableProducts = null;
        break;
      case ApplicableScope.指定商品:
        /** 适用服务类型名称列表，适用范围为指定服务类型时必填 */
        instance.applicableServiceTypes = null;
        /** 适用服务品牌ID列表，适用范围为指定服务品牌时必填 */
        instance.applicableServiceCategories = null;
        /** 适用服务ID列表，适用范围为指定服务时必填 */
        instance.applicableServices = null;
        /** 适用商品类别ID列表，适用范围为指定商品类别时必填 */
        instance.applicableProductTypes = null;
        break;
      default:
        break;
    }
  }

  @BeforeUpdate
  static async updatePassword(instance: MembershipCardType) {
    if (instance.usageLimit < 0) {
      instance.usageLimit = null;
    }

    // 根据卡类型设置相应的属性
    if (instance.type === 'discount') {
      // 折扣卡必须设置折扣率
      if (
        instance.discountRate === undefined ||
        instance.discountRate === null
      ) {
        throw new Error('折扣卡必须设置折扣率');
      }
    } else if (instance.type === 'times') {
      // 次卡必须设置使用次数
      if (instance.usageLimit === undefined || instance.usageLimit === null) {
        throw new Error('次卡必须设置使用次数');
      }
      // 次卡折扣率强制为0
      instance.discountRate = 0;
    }

    // 范围发生变更时，清除多余的信息
    switch (instance.applicableScope) {
      case ApplicableScope.不限:
      case ApplicableScope.所有服务:
      case ApplicableScope.所有商品:
        /** 适用服务类型名称列表，适用范围为指定服务类型时必填 */
        instance.applicableServiceTypes = null;
        /** 适用服务品牌ID列表，适用范围为指定服务品牌时必填 */
        instance.applicableServiceCategories = null;
        /** 适用服务ID列表，适用范围为指定服务时必填 */
        instance.applicableServices = null;
        /** 适用商品类别ID列表，适用范围为指定商品类别时必填 */
        instance.applicableProductTypes = null;
        /** 适用商品ID列表，适用范围为指定商品时必填 */
        instance.applicableProducts = null;
        break;
      case ApplicableScope.指定服务类别:
        /** 适用服务品牌ID列表，适用范围为指定服务品牌时必填 */
        instance.applicableServiceCategories = null;
        /** 适用服务ID列表，适用范围为指定服务时必填 */
        instance.applicableServices = null;
        /** 适用商品类别ID列表，适用范围为指定商品类别时必填 */
        instance.applicableProductTypes = null;
        /** 适用商品ID列表，适用范围为指定商品时必填 */
        instance.applicableProducts = null;
        break;
      case ApplicableScope.指定服务品牌:
        /** 适用服务类型名称列表，适用范围为指定服务类型时必填 */
        instance.applicableServiceTypes = null;
        /** 适用服务ID列表，适用范围为指定服务时必填 */
        instance.applicableServices = null;
        /** 适用商品类别ID列表，适用范围为指定商品类别时必填 */
        instance.applicableProductTypes = null;
        /** 适用商品ID列表，适用范围为指定商品时必填 */
        instance.applicableProducts = null;
        break;
      case ApplicableScope.指定服务:
        /** 适用服务类型名称列表，适用范围为指定服务类型时必填 */
        instance.applicableServiceTypes = null;
        /** 适用服务品牌ID列表，适用范围为指定服务品牌时必填 */
        instance.applicableServiceCategories = null;
        /** 适用商品类别ID列表，适用范围为指定商品类别时必填 */
        instance.applicableProductTypes = null;
        /** 适用商品ID列表，适用范围为指定商品时必填 */
        instance.applicableProducts = null;
        break;
      case ApplicableScope.指定商品类别:
        /** 适用服务类型名称列表，适用范围为指定服务类型时必填 */
        instance.applicableServiceTypes = null;
        /** 适用服务品牌ID列表，适用范围为指定服务品牌时必填 */
        instance.applicableServiceCategories = null;
        /** 适用服务ID列表，适用范围为指定服务时必填 */
        instance.applicableServices = null;
        /** 适用商品ID列表，适用范围为指定商品时必填 */
        instance.applicableProducts = null;
        break;
      case ApplicableScope.指定商品:
        /** 适用服务类型名称列表，适用范围为指定服务类型时必填 */
        instance.applicableServiceTypes = null;
        /** 适用服务品牌ID列表，适用范围为指定服务品牌时必填 */
        instance.applicableServiceCategories = null;
        /** 适用服务ID列表，适用范围为指定服务时必填 */
        instance.applicableServices = null;
        /** 适用商品类别ID列表，适用范围为指定商品类别时必填 */
        instance.applicableProductTypes = null;
        break;
      default:
        break;
    }
  }
}
