import { Controller, Get, Query } from '@midwayjs/core';
import { Dictionary } from '../../entity';

@Controller('/openapi/dictionary')
export class DictionaryController {
  @Get('/')
  async getDictionary(@Query() { type }: { type?: string }) {
    const where = {
      status: 1,
    };
    if (type) {
      where['type'] = type;
    }
    const list = await Dictionary.findAll({
      where,
    });
    return list;
  }
}
