import { Provide, Inject } from '@midwayjs/core';
import {
  OrderDiscountInfo,
  DiscountType,
} from '../entity/order-discount-info.entity';
import { BaseService } from '../common/BaseService';
import { CustomerCouponService } from './customer-coupon.service';
import { CustomerMembershipCardService } from './customer-membership-card.service';
import { CouponUsageRecordService } from './coupon-usage-record.service';
import { MembershipCardUsageRecordService } from './membership-card-usage-record.service';
import { CombinedChangeLogService } from './combined-change-log.service';

@Provide()
export class OrderDiscountInfoService extends BaseService<OrderDiscountInfo> {
  @Inject()
  customerCouponService: CustomerCouponService;

  @Inject()
  customerMembershipCardService: CustomerMembershipCardService;

  @Inject()
  couponUsageRecordService: CouponUsageRecordService;

  @Inject()
  membershipCardUsageRecordService: MembershipCardUsageRecordService;

  @Inject()
  combinedChangeLogService: CombinedChangeLogService;

  constructor() {
    super('订单优惠信息');
  }

  getModel() {
    return OrderDiscountInfo;
  }

  /**
   * 创建订单优惠信息并使用相应的卡券
   * @param orderId 订单ID
   * @param discountInfos 优惠信息列表
   */
  async createOrderDiscountInfos(
    orderId: number,
    discountInfos: Array<{
      discountType: DiscountType;
      discountId: number;
      discountAmount: number;
    }>
  ) {
    if (!discountInfos || discountInfos.length === 0) {
      return [];
    }

    const results = [];

    for (const info of discountInfos) {
      try {
        // 创建订单优惠信息记录
        const discountInfo = await this.create({
          orderId,
          discountType: info.discountType,
          discountId: info.discountId,
          discountAmount: info.discountAmount,
          isRefunded: false,
        });

        // 根据优惠类型使用相应的卡券
        if (info.discountType === DiscountType.COUPON) {
          // 使用代金券
          await this.customerCouponService.useCoupon(info.discountId, orderId);
        } else if (info.discountType === DiscountType.MEMBERSHIP_CARD) {
          // 使用权益卡
          await this.customerMembershipCardService.useCard(
            info.discountId,
            orderId
          );
          // 创建权益卡使用记录
          await this.membershipCardUsageRecordService.createUsageRecord(
            info.discountId,
            orderId
          );
        }

        results.push(discountInfo);
      } catch (error) {
        console.error(`[创建优惠] 处理优惠信息失败：`, error);
        throw error;
      }
    }

    return results;
  }

  /**
   * 退回订单使用的卡券
   * @param orderId 订单ID
   * @param reason 退回原因
   */
  async refundOrderDiscounts(orderId: number, reason: string) {
    // 查找订单的所有优惠信息
    const discountInfos = await this.findAll({
      query: { orderId, isRefunded: false },
    });

    if (
      !discountInfos ||
      !discountInfos.list ||
      discountInfos.list.length === 0
    ) {
      return { refundedCount: 0 };
    }

    let refundedCount = 0;

    for (const info of discountInfos.list) {
      try {
        // 根据优惠类型退回相应的卡券
        if (info.discountType === DiscountType.COUPON) {
          // 使用代金券
          await this.customerCouponService.addTimes(info.discountId, 1);
          // 标记优惠券使用记录为已退回
          await this.couponUsageRecordService.markAsRefunded(orderId);
        } else if (info.discountType === DiscountType.MEMBERSHIP_CARD) {
          // 使用权益卡
          await this.customerMembershipCardService.addTimes(info.discountId, 1);
          // 标记权益卡使用记录为已退回
          await this.membershipCardUsageRecordService.markAsRefunded(orderId);
        }

        // 更新优惠信息为已退回
        await info.update({
          isRefunded: true,
          refundTime: new Date(),
          refundDescription: reason,
        });

        refundedCount++;
      } catch (error) {
        console.error(`[退款] 处理优惠信息失败：`, error);
        throw error;
      }
    }

    return { refundedCount };
  }

  /**
   * 检查订单是否已退回卡券
   * @param orderId 订单ID
   */
  async checkOrderRefundStatus(orderId: number) {
    const discountInfos = await this.findAll({
      query: { orderId },
    });

    if (
      !discountInfos ||
      !discountInfos.list ||
      discountInfos.list.length === 0
    ) {
      return {
        hasDiscountInfos: false,
        allRefunded: false,
        infoCount: 0,
      };
    }

    // 检查是否所有优惠信息都已退回
    const allRefunded = discountInfos.list.every(
      (info: OrderDiscountInfo) => info.isRefunded
    );

    return {
      hasDiscountInfos: true,
      allRefunded,
      infoCount: discountInfos.list.length,
    };
  }

  /**
   * 获取订单的优惠信息
   * @param orderId 订单ID
   */
  async getOrderDiscountInfos(orderId: number) {
    return await this.findAll({
      query: { orderId },
    });
  }

  /**
   * 处理订单优惠信息
   * @param orderId 订单ID
   * @param discountInfos 优惠信息列表
   */
  async processOrderDiscounts(
    orderId: number,
    discountInfos: Array<{
      discountType: DiscountType;
      discountId: number;
      discountAmount: number;
    }>
  ) {
    // 调用创建订单优惠信息方法，同时处理卡券使用
    return await this.createOrderDiscountInfos(orderId, discountInfos);
  }
}
