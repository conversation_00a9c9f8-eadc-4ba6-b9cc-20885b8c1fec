import {
  Table,
  Column,
  Model,
  DataType,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { Customer } from './customer.entity';

export interface CustomerAddressAttrs {
  /** 地址ID */
  id?: number;
  /** 客户ID */
  customerId: number;
  /** 联系人姓名 */
  contactName: string;
  /** 联系人手机号 */
  contactPhone: string;
  /** 省份编码 */
  provinceCode: string;
  /** 省份名称 */
  provinceName: string;
  /** 城市编码 */
  cityCode: string;
  /** 城市名称 */
  cityName: string;
  /** 区县编码 */
  districtCode: string;
  /** 区县名称 */
  districtName: string;
  /** 详细地址 */
  detailAddress: string;
  /** 虚拟字段，与前端配合 */
  addressText?: string;
  /** 虚拟字段，与前端配合，地区编码，6位数 */
  addressCode?: string;
  /** 经度 */
  longitude?: number;
  /** 纬度 */
  latitude?: number;
  /** 是否默认地址 */
  isDefault?: boolean;
  /** 备注 */
  remark?: string;
}

@Table({
  tableName: 'customer_addresses',
  timestamps: true,
  comment: '客户收费地址表',
})
export class CustomerAddress extends Model {
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '地址ID',
  })
  id: number;

  @ForeignKey(() => Customer)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '客户ID',
  })
  customerId: number;

  @Column({
    type: DataType.STRING(50),
    allowNull: false,
    comment: '联系人姓名',
  })
  contactName: string;

  @Column({
    type: DataType.STRING(11),
    allowNull: false,
    comment: '联系人手机号',
  })
  contactPhone: string;

  @Column({
    type: DataType.STRING(6),
    allowNull: false,
    comment: '省份编码',
  })
  provinceCode: string;

  @Column({
    type: DataType.STRING(50),
    allowNull: false,
    comment: '省份名称',
  })
  provinceName: string;

  @Column({
    type: DataType.STRING(6),
    allowNull: false,
    comment: '城市编码',
  })
  cityCode: string;

  @Column({
    type: DataType.STRING(50),
    allowNull: false,
    comment: '城市名称',
  })
  cityName: string;

  @Column({
    type: DataType.STRING(6),
    allowNull: false,
    comment: '区县编码',
  })
  districtCode: string;

  @Column({
    type: DataType.STRING(50),
    allowNull: false,
    comment: '区县名称',
  })
  districtName: string;

  @Column({
    type: DataType.STRING(200),
    allowNull: false,
    comment: '详细地址',
  })
  detailAddress: string;

  /** 虚拟字段，与前端配合 */
  @Column({
    type: DataType.VIRTUAL,
    get() {
      return this.get('detailAddress');
    },
    comment: '地址文本',
  })
  addressText?: string;

  /** 虚拟字段，与前端配合，地区编码，6位数 */
  @Column({
    type: DataType.VIRTUAL,
    get() {
      const provinceCode = this.get('provinceCode');
      const cityCode = this.get('cityCode');
      const districtCode = this.get('districtCode');
      if (!provinceCode || !cityCode || !districtCode) return '';
      return `${provinceCode}${cityCode}${districtCode}`;
    },
    comment: '地址编码',
  })
  addressCode: string;

  @Column({
    type: DataType.DECIMAL(10, 6),
    comment: '经度',
  })
  longitude: number;

  @Column({
    type: DataType.DECIMAL(10, 6),
    comment: '纬度',
  })
  latitude: number;

  @Column({
    type: DataType.BOOLEAN,
    defaultValue: false,
    comment: '是否默认地址',
  })
  isDefault: boolean;

  @Column({
    type: DataType.STRING(200),
    comment: '备注',
  })
  remark: string;

  @BelongsTo(() => Customer)
  customer: Customer;
}
