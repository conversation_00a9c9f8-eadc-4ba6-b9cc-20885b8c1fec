# 简单高性能 WebSocket 服务

## 概述

基于 Midway.js 框架实现的简单高性能 WebSocket 服务，专注于文本消息的实时通信。

## 功能特性

- ✅ WebSocket 连接管理
- ✅ 文本消息实时广播
- ✅ 心跳检测
- ✅ 客户端状态管理
- ✅ HTTP 管理接口
- ✅ 测试页面

## 架构设计

### 核心组件

1. **WebSocket 控制器** (`src/socket/websocket.controller.ts`)
   - 处理连接、消息、断开连接事件
   - 支持文本消息和心跳消息
   - 自动广播消息给所有连接的客户端

2. **WebSocket 服务** (`src/service/websocket.service.ts`)
   - 提供消息广播功能
   - 客户端连接管理
   - 连接状态统计

3. **HTTP 管理接口** (`src/controller/websocket-admin.controller.ts`)
   - 获取服务状态
   - 广播消息
   - 健康检查

4. **测试页面** (`public/websocket-test.html`)
   - 简单的 WebSocket 测试界面
   - 支持连接、发送消息、查看日志

## 快速开始

### 1. 启动服务

```bash
# 开发环境
npm run dev

# 生产环境
npm start
```

### 2. 访问测试页面

打开浏览器访问：`http://localhost:3001/websocket-test.html`

### 3. WebSocket 连接

```javascript
const ws = new WebSocket('ws://localhost:3001');

ws.onopen = function(event) {
    console.log('WebSocket 连接已建立');
};

ws.onmessage = function(event) {
    const data = JSON.parse(event.data);
    console.log('收到消息:', data);
};

ws.onclose = function(event) {
    console.log('WebSocket 连接已关闭');
};
```

## 消息格式

### 基础消息结构

```typescript
interface WebSocketMessage {
    type: string;           // 消息类型
    data?: any;            // 消息数据
    timestamp?: string;    // 时间戳
    clientId?: string;     // 客户端ID
}
```

### 支持的消息类型

#### 1. 连接消息 (connection)

**服务器发送给客户端：**
```json
{
    "type": "connection",
    "data": {
        "clientId": "client_1234567890_abc123",
        "message": "连接成功",
        "timestamp": "2025-01-20T10:00:00.000Z"
    }
}
```

#### 2. 聊天消息 (chat)

**客户端发送：**
```json
{
    "type": "chat",
    "content": "Hello World!"
}
```

**服务器广播：**
```json
{
    "type": "chat",
    "data": {
        "clientId": "client_1234567890_abc123",
        "content": "Hello World!",
        "timestamp": "2025-01-20T10:00:00.000Z"
    }
}
```

#### 3. 心跳消息 (ping/pong)

**客户端发送：**
```json
{
    "type": "ping"
}
```

**服务器响应：**
```json
{
    "type": "pong",
    "timestamp": "2025-01-20T10:00:00.000Z"
}
```

#### 4. 房间管理

**加入房间：**
```json
{
    "type": "join_room",
    "roomId": "room1"
}
```

**离开房间：**
```json
{
    "type": "leave_room",
    "roomId": "room1"
}
```

#### 5. 系统通知 (system_notification)

```json
{
    "type": "system_notification",
    "data": {
        "message": "系统维护通知",
        "level": "warning",
        "timestamp": "2025-01-20T10:00:00.000Z"
    }
}
```

## HTTP 管理接口

### 获取服务状态

```http
GET /api/websocket/status
```

**响应：**
```json
{
    "success": true,
    "data": {
        "isRunning": true,
        "connectedClientCount": 5,
        "connectedClients": [...],
        "serverTime": "2025-01-20T10:00:00.000Z"
    }
}
```

### 广播消息

```http
POST /api/websocket/broadcast
Content-Type: application/json

{
    "message": "系统维护通知",
    "type": "system_notification"
}
```

### 向指定客户端发送消息

```http
POST /api/websocket/send
Content-Type: application/json

{
    "clientId": "client_1234567890_abc123",
    "message": "私人消息",
    "type": "private_message"
}
```

### 断开客户端连接

```http
POST /api/websocket/disconnect
Content-Type: application/json

{
    "clientId": "client_1234567890_abc123",
    "reason": "违规操作"
}
```

### 获取客户端列表

```http
GET /api/websocket/clients
```

### 健康检查

```http
GET /api/websocket/health
```

## 配置说明

### WebSocket 配置

```typescript
// src/config/config.default.ts
export default {
    webSocket: {
        // WebSocket 与 Koa 共享端口
        enableServerHeartbeatCheck: true,    // 启用服务器心跳检查
        serverHeartbeatInterval: 30000,      // 心跳间隔（毫秒）
    }
}
```

### 独立端口配置

如果需要 WebSocket 使用独立端口：

```typescript
export default {
    webSocket: {
        port: 3002,                          // WebSocket 独立端口
        enableServerHeartbeatCheck: true,
        serverHeartbeatInterval: 30000,
    }
}
```

## 开发指南

### 1. 创建自定义消息处理器

```typescript
// src/socket/custom.controller.ts
import { WSController, OnWSMessage } from '@midwayjs/core';

@WSController()
export class CustomSocketController {
    @OnWSMessage('custom_type')
    async handleCustomMessage(data: any) {
        // 处理自定义消息
        return {
            type: 'custom_response',
            data: {
                processed: true,
                originalData: data
            }
        };
    }
}
```

### 2. 使用 WebSocket 服务

```typescript
// src/service/my.service.ts
import { Provide, Inject } from '@midwayjs/core';
import { WebSocketService } from './websocket.service';

@Provide()
export class MyService {
    @Inject()
    webSocketService: WebSocketService;

    async notifyAllUsers(message: string) {
        await this.webSocketService.broadcastToAll({
            type: 'notification',
            data: { message }
        });
    }

    async notifyUser(clientId: string, message: string) {
        await this.webSocketService.sendToClient(clientId, {
            type: 'private_notification',
            data: { message }
        });
    }
}
```

### 3. 房间功能扩展

```typescript
// 加入房间
await this.webSocketService.joinRoom(clientId, roomId);

// 向房间发送消息
await this.webSocketService.sendToRoom(roomId, {
    type: 'room_message',
    data: { message: 'Hello room!' }
});

// 离开房间
await this.webSocketService.leaveRoom(clientId, roomId);
```

## 测试

### 运行单元测试

```bash
npm test
```

### 运行 WebSocket 测试

```bash
npm test -- test/socket/websocket.test.ts
```

### 使用测试页面

1. 启动服务：`npm run dev`
2. 打开浏览器：`http://localhost:3001/websocket-test.html`
3. 点击"连接"按钮建立 WebSocket 连接
4. 发送各种类型的消息进行测试

## 部署注意事项

### 1. 反向代理配置

如果使用 Nginx 作为反向代理，需要添加 WebSocket 支持：

```nginx
location / {
    proxy_pass http://localhost:3001;
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection "upgrade";
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
}
```

### 2. 负载均衡

在多实例部署时，需要考虑 WebSocket 连接的粘性会话或使用 Redis 等外部存储来同步状态。

### 3. 监控

建议监控以下指标：
- 连接数量
- 消息发送/接收速率
- 连接建立/断开频率
- 错误率

## 故障排除

### 常见问题

1. **连接失败**
   - 检查端口是否正确
   - 确认防火墙设置
   - 验证 WebSocket 配置

2. **消息丢失**
   - 检查网络连接
   - 验证消息格式
   - 查看服务器日志

3. **性能问题**
   - 监控连接数量
   - 检查消息大小
   - 优化消息处理逻辑

### 日志查看

```bash
# 查看应用日志
tail -f logs/my-midway-project/midway-core.log

# 查看错误日志
tail -f logs/my-midway-project/common-error.log
```

## 扩展功能

### 1. 消息持久化

可以集成数据库来持久化聊天消息：

```typescript
@OnWSMessage('chat')
async handleChatMessage(data: any) {
    // 保存到数据库
    await this.chatService.saveMessage(data);
    
    // 广播消息
    return await this.handleChatMessage(data);
}
```

### 2. 用户认证

可以集成 JWT 认证：

```typescript
@OnWSConnection()
async onConnection(socket: Context, request: http.IncomingMessage) {
    const token = this.extractTokenFromRequest(request);
    const user = await this.authService.verifyToken(token);
    
    socket.user = user;
    // ... 其他逻辑
}
```

### 3. 消息加密

可以添加端到端加密功能来保护敏感消息。

## 相关链接

- [Midway.js WebSocket 文档](https://www.midwayjs.org/docs/extensions/ws)
- [WebSocket API 文档](https://developer.mozilla.org/en-US/docs/Web/API/WebSocket)
- [ws 库文档](https://github.com/websockets/ws)
