import { Provide } from '@midwayjs/core';
import { ProductType } from '../entity/product-type.entity';
import { BaseService } from '../common/BaseService';

@Provide()
export class ProductTypeService extends BaseService<ProductType> {
  constructor() {
    super('商品类别');
  }

  getModel() {
    return ProductType;
  }

  async findByName(name: string) {
    return await this.findOne({ where: { name } });
  }

  async findWithChildren() {
    return await this.findAll({
      query: {},
      include: ['children'],
    });
  }

  async findWithProducts() {
    return await this.findAll({
      query: {},
      include: ['products'],
    });
  }

  async findRootCategories() {
    return await this.findAll({
      query: {
        parentId: null,
      },
      include: ['children'],
    });
  }

  async updateOrderIndex(id: number, orderIndex: number) {
    return await this.update({ id }, { orderIndex });
  }

  async updateIcon(id: number, icon: string) {
    return await this.update({ id }, { icon });
  }
}
