import { Provide } from '@midwayjs/core';
import { BaseService } from '../common/BaseService';
import {
  CombinedChangeLog,
  CombinedChangeLogAttributes,
  CombinedChangeType,
} from '../entity/combined-change-log.entity';
import { Coupon, Customer, MembershipCardType, Order } from '../entity';

@Provide()
export class CombinedChangeLogService extends BaseService<CombinedChangeLog> {
  getModel() {
    return CombinedChangeLog;
  }

  /**
   * 创建卡券变更日志（通用方法）
   * @param {object} logData - 日志数据
   * @param {string} logData.changeType - 变更类型
   * @param {number} [logData.userCouponId] - 关联的用户代金券ID
   * @param {number} [logData.cardId] - 关联的用户权益卡ID
   * @param {number} [logData.couponId] - 关联的代金券ID
   * @param {number} [logData.cardTypeId] - 关联的权益卡类型Id
   * @param {number} logData.customerId - 关联的用户ID
   * @param {number} [logData.orderId] - 关联的订单ID
   * @param {string} [logData.description] - 变更描述
   * @param {number} [logData.operatorId] - 操作人ID
   * @returns 创建的日志记录
   */
  async createLog(data: {
    /** 变更类型 */
    changeType: CombinedChangeType;
    /** 关联的用户代金券ID */
    userCouponId?: number;
    /** 关联的用户权益卡ID */
    cardId?: number;
    /** 关联的代金券ID */
    couponId?: number;
    /** 关联的权益卡类型Id */
    cardTypeId?: number;
    /** 关联的用户ID */
    customerId: number;
    /** 关联的订单ID */
    orderId?: number;
    /** 变更描述 */
    description?: string;
    /** 操作人ID */
    operatorId?: number;
    /** 变更前的状态 */
    beforeStatus?: string;
    /** 变更后的状态 */
    afterStatus?: string;
    /** 变更前的剩余次数 */
    beforeRemainTimes?: string;
    /** 变更后的剩余次数 */
    afterRemainTimes?: string;
  }) {
    const {
      /** 关联的代金券ID */
      couponId,
      /** 关联的权益卡类型Id */
      cardTypeId,
      /** 关联的用户ID */
      customerId,
      /** 关联的订单ID */
      orderId,
    } = data;
    const info: Omit<CombinedChangeLogAttributes, 'id'> = { ...data };
    if (couponId) {
      const coupon = await Coupon.findByPk(couponId);
      if (coupon) {
        info.couponId = coupon.id;
        info.couponName = `${coupon.amount}元代金券`;
        info.couponAmount = coupon.amount;
      }
      if (cardTypeId) {
        const cardType = await MembershipCardType.findByPk(cardTypeId);
        if (cardType) {
          info.cardTypeId = cardType.id;
          info.cardTypeName = cardType.name;
          info.cardTypePrice = cardType.price;
        }
      }
      if (customerId) {
        const customer = await Customer.findByPk(customerId);
        if (customer) {
          info.customerId = customer.id;
          info.customerName = customer.nickname;
          info.customerPhone = customer.phone;
        }
      }
      if (orderId) {
        const order = await Order.findByPk(orderId);
        if (order) {
          info.orderId = order.id;
          info.orderNo = order.sn;
        }
      }
    }
    return await this.create(info);
  }
}
