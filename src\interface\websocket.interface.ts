/**
 * WebSocket 相关接口定义
 */

/**
 * 客户端信息接口
 */
export interface IClientInfo {
  /** 客户端唯一标识 */
  id: string;
  /** 客户端IP地址 */
  ip?: string;
  /** 用户代理信息 */
  userAgent?: string;
  /** 连接时间 */
  connectedAt: Date;
  /** 用户ID（如果已登录） */
  userId?: number;
  /** 用户名（如果已登录） */
  username?: string;
  /** 客户端所在的房间列表 */
  rooms?: string[];
  /** 额外的元数据 */
  metadata?: Record<string, any>;
}

/**
 * WebSocket 消息基础接口
 */
export interface IWebSocketMessage {
  /** 消息类型 */
  type: string;
  /** 消息数据 */
  data?: any;
  /** 时间戳 */
  timestamp?: string;
  /** 消息ID */
  messageId?: string;
  /** 发送者客户端ID */
  clientId?: string;
}

/**
 * 聊天消息接口
 */
export interface IChatMessage extends IWebSocketMessage {
  type: 'chat';
  data: {
    /** 消息内容 */
    content: string;
    /** 发送者客户端ID */
    clientId: string;
    /** 发送者用户名 */
    username?: string;
    /** 房间ID（如果是房间消息） */
    roomId?: string;
    /** 消息类型：text, image, file 等 */
    contentType?: 'text' | 'image' | 'file' | 'emoji';
    /** 附件信息（如果有） */
    attachment?: {
      url: string;
      filename: string;
      size: number;
      mimeType: string;
    };
  };
}

/**
 * 系统通知消息接口
 */
export interface ISystemNotification extends IWebSocketMessage {
  type: 'system_notification';
  data: {
    /** 通知消息 */
    message: string;
    /** 通知级别 */
    level?: 'info' | 'warning' | 'error' | 'success';
    /** 是否需要用户确认 */
    requireConfirmation?: boolean;
  };
}

/**
 * 用户状态变更消息接口
 */
export interface IUserStatusMessage extends IWebSocketMessage {
  type: 'user_joined' | 'user_left' | 'user_status_changed';
  data: {
    /** 用户客户端ID */
    clientId: string;
    /** 用户名 */
    username?: string;
    /** 状态信息 */
    message: string;
    /** 用户状态 */
    status?: 'online' | 'offline' | 'away' | 'busy';
  };
}

/**
 * 房间相关消息接口
 */
export interface IRoomMessage extends IWebSocketMessage {
  type: 'join_room' | 'leave_room' | 'room_joined' | 'room_left' | 'room_info';
  data: {
    /** 房间ID */
    roomId: string;
    /** 客户端ID */
    clientId?: string;
    /** 消息内容 */
    message?: string;
    /** 房间信息 */
    roomInfo?: {
      name: string;
      description?: string;
      memberCount: number;
      members: Array<{
        clientId: string;
        username?: string;
        joinedAt: string;
      }>;
    };
  };
}

/**
 * 心跳消息接口
 */
export interface IHeartbeatMessage extends IWebSocketMessage {
  type: 'ping' | 'pong';
  data?: {
    /** 服务器时间 */
    serverTime?: string;
    /** 延迟信息 */
    latency?: number;
  };
}

/**
 * 错误消息接口
 */
export interface IErrorMessage extends IWebSocketMessage {
  type: 'error';
  data: {
    /** 错误代码 */
    code: string;
    /** 错误消息 */
    message: string;
    /** 错误详情 */
    details?: any;
    /** 原始消息（如果是响应错误） */
    originalMessage?: any;
  };
}

/**
 * 连接状态消息接口
 */
export interface IConnectionMessage extends IWebSocketMessage {
  type: 'connection' | 'disconnect' | 'reconnect';
  data: {
    /** 客户端ID */
    clientId: string;
    /** 状态消息 */
    message: string;
    /** 断开原因（如果是断开连接） */
    reason?: string;
    /** 服务器信息 */
    serverInfo?: {
      version: string;
      supportedFeatures: string[];
    };
  };
}

/**
 * 文件传输消息接口
 */
export interface IFileTransferMessage extends IWebSocketMessage {
  type: 'file_upload' | 'file_download' | 'file_transfer_progress' | 'file_transfer_complete';
  data: {
    /** 文件ID */
    fileId: string;
    /** 文件名 */
    filename: string;
    /** 文件大小 */
    size: number;
    /** MIME类型 */
    mimeType: string;
    /** 传输进度（0-100） */
    progress?: number;
    /** 文件URL（传输完成后） */
    url?: string;
    /** 错误信息（如果传输失败） */
    error?: string;
  };
}

/**
 * WebSocket 事件类型枚举
 */
export enum WebSocketEventType {
  CONNECTION = 'connection',
  DISCONNECT = 'disconnect',
  MESSAGE = 'message',
  CHAT = 'chat',
  SYSTEM_NOTIFICATION = 'system_notification',
  USER_JOINED = 'user_joined',
  USER_LEFT = 'user_left',
  USER_STATUS_CHANGED = 'user_status_changed',
  JOIN_ROOM = 'join_room',
  LEAVE_ROOM = 'leave_room',
  ROOM_JOINED = 'room_joined',
  ROOM_LEFT = 'room_left',
  ROOM_INFO = 'room_info',
  PING = 'ping',
  PONG = 'pong',
  ERROR = 'error',
  FILE_UPLOAD = 'file_upload',
  FILE_DOWNLOAD = 'file_download',
  FILE_TRANSFER_PROGRESS = 'file_transfer_progress',
  FILE_TRANSFER_COMPLETE = 'file_transfer_complete',
}

/**
 * WebSocket 服务配置接口
 */
export interface IWebSocketConfig {
  /** 端口号 */
  port?: number;
  /** 是否启用心跳检查 */
  enableServerHeartbeatCheck?: boolean;
  /** 心跳检查间隔（毫秒） */
  serverHeartbeatInterval?: number;
  /** 最大连接数 */
  maxConnections?: number;
  /** 消息大小限制（字节） */
  maxMessageSize?: number;
  /** 是否启用压缩 */
  compression?: boolean;
  /** 允许的源域名 */
  allowedOrigins?: string[];
}

/**
 * 房间信息接口
 */
export interface IRoomInfo {
  /** 房间ID */
  id: string;
  /** 房间名称 */
  name: string;
  /** 房间描述 */
  description?: string;
  /** 房间类型 */
  type: 'public' | 'private' | 'protected';
  /** 房间密码（如果是受保护的房间） */
  password?: string;
  /** 最大成员数 */
  maxMembers?: number;
  /** 当前成员数 */
  memberCount: number;
  /** 房间创建者 */
  creator: string;
  /** 房间创建时间 */
  createdAt: Date;
  /** 房间成员列表 */
  members: Array<{
    clientId: string;
    username?: string;
    role: 'owner' | 'admin' | 'member';
    joinedAt: Date;
  }>;
}

/**
 * WebSocket 统计信息接口
 */
export interface IWebSocketStats {
  /** 当前连接数 */
  connectedClients: number;
  /** 总连接数（历史） */
  totalConnections: number;
  /** 消息发送总数 */
  messagesSent: number;
  /** 消息接收总数 */
  messagesReceived: number;
  /** 服务启动时间 */
  serverStartTime: Date;
  /** 房间数量 */
  roomCount: number;
  /** 平均延迟（毫秒） */
  averageLatency?: number;
}
