import {
  Table,
  Column,
  Model,
  DataType,
  BelongsTo,
  ForeignKey,
} from 'sequelize-typescript';
import { Customer } from './customer.entity';

export interface PetAttributes {
  /** 宠物ID */
  id: number;
  /** 关联用户ID */
  customerId: number;
  /** 宠物名称 */
  name: string;
  /** 头像 */
  avatar?: string;
  /** 宠物类型，例如 猫、狗 */
  type: string;
  /** 品种，例如金毛 */
  breed?: string;
  /** 性别：0-未知 1-男 2-女 */
  gender: number;
  /** 出生年月 */
  birthday?: Date;
  /** 年龄（月），虚拟字段 */
  bri?: number;
  /** 毛发类型 */
  hairType: string;
  /** 体重Kg */
  weight?: number;
  /** 体重 */
  weightType?: string;
  /** 是否疫苗 */
  isVaccine?: boolean;
  /** 是否绝育 */
  isSterilization?: boolean;
  /** 是否驱虫 */
  isRepellent?: boolean;
  /** 排序值 */
  orderIndex: number;
  /** 关联客户信息 */
  customer?: Customer;
}

@Table({ tableName: 'pets', timestamps: true, comment: '宠物表' })
export class Pet extends Model<PetAttributes> implements PetAttributes {
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '宠物ID',
  })
  id: number;

  @ForeignKey(() => Customer)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '关联用户ID',
  })
  customerId: number;

  @Column({
    type: DataType.STRING,
    allowNull: false,
    comment: '宠物名称',
  })
  name: string;

  @Column({
    type: DataType.STRING,
    comment: '头像',
  })
  avatar?: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
    comment: '宠物类型',
  })
  type: string;

  @Column({
    type: DataType.STRING,
    comment: '品种',
  })
  breed?: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: '性别：0-未知 1-男 2-女',
  })
  gender: number;

  @Column({
    type: DataType.DATE,
    comment: '出生年月',
  })
  birthday?: Date;

  @Column({
    type: DataType.VIRTUAL,
    get() {
      // 计算年龄，返回月数
      const birthday = this.getDataValue('birthday');
      if (birthday) {
        const now = new Date();
        const birthDate = new Date(birthday);
        const ageInMilliseconds = now.getTime() - birthDate.getTime();
        const ageInMonths = Math.floor(
          ageInMilliseconds / (1000 * 60 * 60 * 24 * 30)
        );
        return ageInMonths;
      } else {
        return 0;
      }
    },
    comment: '年龄（月）',
  })
  bri?: number;

  @Column({
    type: DataType.STRING,
    allowNull: false,
    comment: '毛发类型',
  })
  hairType: string;

  @Column({
    type: DataType.FLOAT,
    comment: '体重Kg',
  })
  weight?: number;

  @Column({
    type: DataType.STRING,
    comment: '体重范围，需要与字典匹配',
  })
  weightType?: string;

  @Column({
    type: DataType.BOOLEAN,
    comment: '是否疫苗',
  })
  isVaccine?: boolean;

  @Column({
    type: DataType.BOOLEAN,
    comment: '是否绝育',
  })
  isSterilization?: boolean;

  @Column({
    type: DataType.BOOLEAN,
    comment: '是否驱虫',
  })
  isRepellent?: boolean;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: '排序值',
  })
  orderIndex: number;

  @BelongsTo(() => Customer)
  customer?: Customer;
}
