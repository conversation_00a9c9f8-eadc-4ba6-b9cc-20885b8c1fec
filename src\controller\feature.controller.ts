import {
  Body,
  Controller,
  Del,
  Get,
  Inject,
  Param,
  Post,
  Put,
  Query,
} from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { FeatureService } from '../service/feature.service';
import { CustomError } from '../error/custom.error';
import { Op } from 'sequelize';
import { Permission } from '../entity';

@Controller('/features')
export class FeatureController {
  @Inject()
  ctx: Context;

  @Inject()
  service: FeatureService;

  @Get('/', { summary: '查询功能列表' })
  async index(@Query() query: any) {
    let { offset, limit } = query;
    const { offset: o, limit: l, current, pageSize, ...queryInfo } = query;
    void o;
    void l;

    if (current && pageSize) {
      offset = (current - 1) * pageSize;
      limit = pageSize;
    }
    // name支持模糊查询
    if (queryInfo.name) {
      queryInfo.name = {
        [Op.like]: `%${queryInfo.name}%`,
      };
    }
    return this.service.findAll({
      query: queryInfo,
      offset,
      limit,
      order: [['orderIndex', 'ASC']],
      include: [
        {
          model: Permission,
        },
      ],
    });
  }

  @Get('/:code', { summary: '按code查询功能' })
  async show(@Param('code') code: string) {
    const res = await this.service.findById(code);
    if (!res) {
      throw new CustomError('未找到指定功能');
    }
    return res;
  }

  @Post('/', { summary: '新增功能' })
  async create(@Body() info: any) {
    const res = await this.service.create(info);
    return res;
  }

  @Put('/:code', { summary: '更新功能' })
  async update(@Param('code') code: string, @Body() body: any) {
    await this.service.update({ code }, body);
    return true;
  }

  @Del('/:code', { summary: '删除功能' })
  async destroy(@Param('code') code: string) {
    await this.service.delete({ code });
    return true;
  }
}
