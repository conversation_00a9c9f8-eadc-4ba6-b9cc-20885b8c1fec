import {
  Table,
  Column,
  Model,
  DataType,
  BelongsTo,
  ForeignKey,
  HasMany,
} from 'sequelize-typescript';
import { Customer } from './customer.entity';
import { Coupon } from './coupon.entity';
import { CouponUsageRecord } from './coupon-usage-record.entity';

export interface CustomerCouponAttributes {
  /** 客户代金券唯一标识 */
  id: number;
  /** 关联客户 */
  customerId: number;
  /** 关联代金券 */
  couponId: number;
  /** 领取时间 */
  receiveTime: Date;
  /** 到期时间，null表示无到期时间 */
  expiryTime?: Date;
  /** 剩余使用次数，-1表示不限次数 */
  remainTimes: number;
  /** 状态：active-有效，used-已使用，expired-已过期 */
  status: 'active' | 'used' | 'expired';
  /** 最后一次使用时间 */
  lastUseTime?: Date;
  /** 关联的客户信息 */
  customer?: Customer;
  /** 关联的代金券信息 */
  coupon?: Coupon;
  /** 使用记录 */
  usageRecords?: CouponUsageRecord[];
}

@Table({
  tableName: 'customer_coupons',
  timestamps: true,
  comment: '客户代金券表',
})
export class CustomerCoupon
  extends Model<CustomerCouponAttributes>
  implements CustomerCouponAttributes
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '客户代金券唯一标识',
  })
  id: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '关联客户',
  })
  @ForeignKey(() => Customer)
  customerId: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '关联代金券',
  })
  @ForeignKey(() => Coupon)
  couponId: number;

  @Column({
    type: DataType.DATE,
    allowNull: false,
    defaultValue: DataType.NOW,
    comment: '领取时间',
  })
  receiveTime: Date;

  @Column({
    type: DataType.DATE,
    comment: '到期时间，null表示无到期时间',
  })
  expiryTime?: Date;

  @Column({
    type: DataType.INTEGER,
    defaultValue: 0,
    comment: '剩余使用次数，-1表示不限次数',
  })
  remainTimes: number;

  @Column({
    type: DataType.ENUM('active', 'used', 'expired'),
    defaultValue: 'active',
    comment: '状态：active-有效，used-已使用，expired-已过期',
  })
  status: 'active' | 'used' | 'expired';

  @Column({
    type: DataType.DATE,
    comment: '最后一次使用时间',
  })
  lastUseTime?: Date;

  @BelongsTo(() => Customer)
  customer?: Customer;

  @BelongsTo(() => Coupon)
  coupon?: Coupon;

  @HasMany(() => CouponUsageRecord)
  usageRecords?: CouponUsageRecord[];
}
