import { Table, Column, Model, ForeignKey } from 'sequelize-typescript';
import { Role } from './role.entity';
import { Permission } from './permission.entity';

@Table({
  tableName: 'role_permissions',
  timestamps: false,
  comment: '角色权限关联表',
})
export class RolePermission extends Model {
  @ForeignKey(() => Role)
  @Column
  roleId: number;

  @ForeignKey(() => Permission)
  @Column
  permissionId: number;
}
