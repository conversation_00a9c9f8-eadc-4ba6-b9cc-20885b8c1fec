{"name": "my-midway-project", "version": "1.0.0", "description": "", "private": true, "dependencies": {"@midwayjs/axios": "^3.20.3", "@midwayjs/bootstrap": "^3.12.0", "@midwayjs/cache-manager": "^3.20.4", "@midwayjs/core": "^3.12.0", "@midwayjs/cron": "^3.20.4", "@midwayjs/info": "^3.12.0", "@midwayjs/jwt": "^3.20.3", "@midwayjs/koa": "^3.12.0", "@midwayjs/logger": "^3.1.0", "@midwayjs/redis": "^3.20.3", "@midwayjs/sequelize": "^3.20.3", "@midwayjs/static-file": "^3.20.5", "@midwayjs/task": "^3.6.0", "@midwayjs/validate": "^3.12.0", "@midwayjs/ws": "^3.20.3", "bcryptjs": "^3.0.2", "cos-nodejs-sdk-v5": "^2.14.6", "crypto-js": "^4.2.0", "lodash": "^4.17.21", "mysql2": "^3.12.0", "qcloud-cos-sts": "^3.1.1", "sequelize": "^6.37.6", "sequelize-typescript": "^2.1.6"}, "devDependencies": {"@midwayjs/mock": "^3.12.0", "@types/crypto-js": "^4.2.2", "@types/jest": "^29.2.0", "@types/lodash": "^4.17.16", "@types/node": "14", "cross-env": "^6.0.0", "jest": "^29.2.2", "mwts": "^1.3.0", "mwtsc": "^1.4.0", "ts-jest": "^29.0.3", "typescript": "~4.8.0"}, "engines": {"node": ">=12.0.0"}, "scripts": {"start": "NODE_ENV=production node ./bootstrap.js", "dev": "cross-env NODE_ENV=local mwtsc --watch --run @midwayjs/mock/app.js", "test": "cross-env NODE_ENV=unittest jest", "cov": "jest --coverage", "lint": "mwts check", "lint:fix": "mwts fix", "ci": "npm run cov", "build": "mwtsc --cleanOutDir"}, "repository": {"type": "git", "url": ""}, "author": "anonymous", "license": "MIT"}