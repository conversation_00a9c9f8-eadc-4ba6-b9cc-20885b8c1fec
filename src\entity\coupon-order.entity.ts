import {
  Table,
  Column,
  Model,
  DataType,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { Customer } from './customer.entity';
import { Coupon } from './coupon.entity';

/**
 * 代金券订单状态枚举
 */
export enum CouponOrderStatus {
  /** 待付款 */
  PENDING_PAYMENT = 'pending_payment',
  /** 已付款 */
  PAID = 'paid',
  /** 已取消 */
  CANCELLED = 'cancelled',
  /** 已退款 */
  REFUNDED = 'refunded',
}

/**
 * 代金券订单属性接口
 */
export interface CouponOrderAttributes {
  /** 订单ID */
  id: number;
  /** 订单编号 */
  sn: string;
  /** 关联的用户ID */
  customerId: number;
  /** 关联的代金券ID */
  couponId: number;
  /** 订单金额 */
  amount: number;
  /** 订单状态 */
  status: CouponOrderStatus;
  /** 创建时间 */
  createdAt?: Date;
  /** 支付时间 */
  payTime?: Date;
  /** 取消时间 */
  cancelTime?: Date;
  /** 退款时间 */
  refundTime?: Date;
  /** 微信支付预支付ID */
  prepayId?: string;
  /** 备注 */
  remark?: string;
  /** 关联的用户 */
  customer?: Customer;
  /** 关联的代金券 */
  coupon?: Coupon;
}

/**
 * 代金券订单表
 */
@Table({
  tableName: 'coupon_orders',
  timestamps: true,
  comment: '代金券订单表',
})
export class CouponOrder
  extends Model<CouponOrderAttributes>
  implements CouponOrderAttributes
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '订单ID',
  })
  id: number;

  @Column({
    type: DataType.STRING(50),
    allowNull: false,
    unique: true,
    comment: '订单编号',
  })
  sn: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '关联的用户ID',
  })
  @ForeignKey(() => Customer)
  customerId: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '关联的代金券ID',
  })
  @ForeignKey(() => Coupon)
  couponId: number;

  @Column({
    type: DataType.DECIMAL(8, 2),
    allowNull: false,
    comment: '订单金额',
  })
  amount: number;

  @Column({
    type: DataType.ENUM(...Object.values(CouponOrderStatus)),
    allowNull: false,
    defaultValue: CouponOrderStatus.PENDING_PAYMENT,
    comment: '订单状态',
  })
  status: CouponOrderStatus;

  @Column({
    type: DataType.DATE,
    comment: '支付时间',
  })
  payTime?: Date;

  @Column({
    type: DataType.DATE,
    comment: '取消时间',
  })
  cancelTime?: Date;

  @Column({
    type: DataType.DATE,
    comment: '退款时间',
  })
  refundTime?: Date;

  @Column({
    type: DataType.STRING(100),
    comment: '微信支付预支付ID',
  })
  prepayId?: string;

  @Column({
    type: DataType.STRING(255),
    comment: '备注',
  })
  remark?: string;

  @BelongsTo(() => Customer)
  customer?: Customer;

  @BelongsTo(() => Coupon)
  coupon?: Coupon;
}
