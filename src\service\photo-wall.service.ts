import { Provide } from '@midwayjs/core';
import { PhotoWall } from '../entity/photo-wall.entity';
import { BaseService } from '../common/BaseService';
import { Order } from '../entity/order.entity';

@Provide()
export class PhotoWallService extends BaseService<PhotoWall> {
  constructor() {
    super('照片墙');
  }

  getModel() {
    return PhotoWall;
  }

  async findByOrder(orderId: number) {
    return await this.findOne({
      where: { orderId },
      include: [Order],
    });
  }

  async findLatest(limit = 10) {
    return await this.findAll({
      query: {},
      limit,
      order: [['createdAt', 'DESC']],
      include: [Order],
    });
  }
}
