import { Provide } from '@midwayjs/core';
import { Employee } from '../entity/employee.entity';
import { BaseService } from '../common/BaseService';
import { Vehicle } from '../entity/vehicle.entity';
import { Order } from '../entity/order.entity';

@Provide()
export class EmployeeService extends BaseService<Employee> {
  constructor() {
    super('员工');
  }

  getModel() {
    return Employee;
  }

  async findByPhone(phone: string) {
    return await this.findOne({ where: { phone } });
  }

  async updateWallet(id: number, amount: number) {
    const employee = await this.findById(id);
    if (!employee) {
      throw new Error('员工不存在');
    }
    return await employee.update({
      walletBalance: employee.walletBalance + amount,
    });
  }

  async assignVehicle(id: number, vehicleId: number) {
    return await this.update({ id }, { vehicleId });
  }

  async getVehicle(employeeId: number) {
    const employee = await Employee.findByPk(employeeId, {
      include: [Vehicle],
    });
    return employee?.vehicle;
  }

  async getOrders(employeeId: number) {
    const employee = await Employee.findByPk(employeeId, {
      include: [Order],
    });
    return employee?.orders || [];
  }
}
