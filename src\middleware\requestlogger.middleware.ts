/*
 * @Description: 请求日志中间件
 * @Date: 2025-01-09 08:43:18
 * @LastEditors: 朱鹏亮 <EMAIL>
 * @LastEditTime: 2025-01-09 08:43:30
 */
import { IMiddleware, Middleware } from '@midwayjs/core';
import { Context, NextFunction } from '@midwayjs/koa';

@Middleware()
export class RequestLoggerMiddleware
  implements IMiddleware<Context, NextFunction>
{
  resolve() {
    return async (ctx: Context, next: NextFunction) => {
      console.dir({
        url: ctx.request.url,
        method: ctx.request.method,
        body: ctx.request.body || {},
        query: ctx.request.query || {},
        params: ctx.params,
        // headers: ctx.request.headers,
        ip: ctx.request.ip,
      });
      await next();
    };
  }
}
