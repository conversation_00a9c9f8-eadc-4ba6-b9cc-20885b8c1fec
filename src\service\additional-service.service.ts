import { Inject, Provide } from '@midwayjs/core';
import { AdditionalService } from '../entity';
import { BaseService } from '../common/BaseService';
import { Context } from '@midwayjs/koa';

@Provide()
export class AdditionalServiceService extends BaseService<AdditionalService> {
  @Inject()
  ctx: Context;

  constructor() {
    super('增项服务');
  }
  getModel = () => {
    return AdditionalService;
  };
}
