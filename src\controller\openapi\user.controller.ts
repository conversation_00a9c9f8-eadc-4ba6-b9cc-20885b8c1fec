import { Body, Controller, Inject, Post } from '@midwayjs/core';
import { Customer, CustomerAttributes } from '../../entity';
import { EmployeeService } from '../../service/employee.service';
import { JwtService } from '@midwayjs/jwt';

@Controller('/openapi/user')
export class UserController {
  @Inject()
  jwtService: JwtService;

  @Inject()
  employeeService: EmployeeService;

  @Post('/register')
  async register(@Body() body: RegisterUserInfo) {
    const { openid, avatarUrl, nickName, phone, gender } = body;
    const userInfo: Omit<CustomerAttributes, 'id'> = {
      openid,
      phone,
      nickname: nickName,
      avatar: avatarUrl,
      gender,
      memberStatus: 0,
      points: 0,
      lastLoginTime: new Date(),
      status: 1,
      promotionCode: '',
    };
    const user = await Customer.create(userInfo);
    return user;
  }

  @Post('/login-for-employee', { summary: '员工登录' })
  async loginForEmployee(@Body('phone') phone: string) {
    const user = await this.employeeService.findByPhone(phone);
    if (!user) {
      return {
        code: 404,
        message: '用户不存在',
      };
    }
    // 生成访问 token
    const accessPayload = {
      userId: user.id,
      type: 'employee',
    };
    const token = this.jwtService.signSync(accessPayload, { expiresIn: '2h' });

    // 生成刷新 token，用于刷新访问 token，7 天过期，暂时未使用
    const refreshPayload = {
      userId: 'employee_' + user.id,
      type: 'refresh',
    };
    const refreshToken = this.jwtService.signSync(refreshPayload, {
      expiresIn: '7d',
    });

    return {
      token,
      refreshToken,
      user,
    };
  }
}
