import { Provide } from '@midwayjs/core';
import { MembershipCardType } from '../entity/membership-card-type.entity';
import { BaseService } from '../common/BaseService';

@Provide()
export class MembershipCardTypeService extends BaseService<MembershipCardType> {
  constructor() {
    super('权益卡类型');
  }

  getModel() {
    return MembershipCardType;
  }

  /** 按名称查找 */
  async findByName(name: string) {
    return await this.findOne({ where: { name } });
  }

  /** 按类型查找 */
  async findByType(type: 'discount' | 'times') {
    return await this.findAll({
      query: { type },
      order: [['createdAt', 'DESC']],
    });
  }

  /** 更新折扣率 */
  async updateDiscountRate(id: number, discountRate: number) {
    return await this.update({ id }, { discountRate });
  }

  /** 更新使用次数 */
  async updateUsageLimit(id: number, usageLimit: number) {
    return await this.update({ id }, { usageLimit });
  }

  /** 更新卡类型 */
  async updateCardType(id: number, type: 'discount' | 'times', data: any = {}) {
    const updateData = { type, ...data };

    // 根据类型设置默认值
    if (type === 'times') {
      // 次卡折扣率强制为0
      updateData.discountRate = 0;
    }

    return await this.update({ id }, updateData);
  }
}
