/*
 * @Description: 天翼云对象存储签名工具类
 * @Date: 2025-04-16 08:20:28
 * @LastEditors: 朱鹏亮 <EMAIL>
 * @LastEditTime: 2025-04-16 12:50:55
 */
import * as CryptoJS from 'crypto-js';
import * as crypto from 'crypto';
import { Config, Provide } from '@midwayjs/core';
// import { v4 as uuidv4 } from 'uuid';

interface RequestObject {
  url: URL;
  headers: Headers;
  body?: Record<string, any>;
}

interface ReturnObject {
  'Eop-date': string;
  'ctyun-eop-request-id': string;
  'Eop-Authorization': string;
  // 'User-Agent': string;
}

interface Headers {
  set: (name: string, value: string) => void;
}

@Provide()
export class ZosSigner {
  @Config('zos')
  config: {
    endpoint: string;
    ak: string;
    sk: string;
  };

  private encodeQueryParams(request: RequestObject): void {
    const url = request.url;
    const params = Array.from(url.searchParams.entries());

    params.forEach(([key, value]) => {
      url.searchParams.set(key, encodeURIComponent(value));
    });
  }

  private formatISO8601() {
    const date = new Date();
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');

    return `${year}${month}${day}T${hours}${minutes}${seconds}Z`;
  }

  private queryparam(url: string) {
    const urlStr = url.toString().match('(https|http)://[^/]+(/.+)')[2];
    if (urlStr.indexOf('?') !== -1) {
      let param = urlStr.substring(urlStr.indexOf('?') + 1);
      if (param.indexOf('#') !== -1) {
        param = param.substring(0, param.indexOf('#'));
      }
      const arr = param.split('&');
      arr.sort();
      return arr.join('&');
    } else {
      return '';
    }
  }

  private getbody(body?: any) {
    if (Object.keys(body).length === 0) {
      return CryptoJS.enc.Hex.stringify(CryptoJS.SHA256(''));
    } else {
      return CryptoJS.enc.Hex.stringify(CryptoJS.SHA256(JSON.stringify(body)));
    }
  }

  public signRequest(request: RequestObject): ReturnObject {
    this.encodeQueryParams(request);

    const dstr = this.formatISO8601();
    const uid = crypto.randomUUID();

    const header_str =
      'ctyun-eop-request-id:' + uid + '\neop-date:' + dstr + '\n';

    const signature_str =
      header_str +
      '\n' +
      this.queryparam(request.url.href) +
      '\n' +
      this.getbody(request.body || {});

    const b64h1 = CryptoJS.HmacSHA256(
      encodeURI(dstr),
      encodeURI(this.config.sk)
    );
    const b64h2 = CryptoJS.HmacSHA256(this.config.ak, b64h1);
    const b64h3 = CryptoJS.HmacSHA256(
      dstr.substring(0, dstr.indexOf('T')),
      b64h2
    );

    const s = CryptoJS.enc.Base64.stringify(
      CryptoJS.HmacSHA256(signature_str, b64h3)
    );

    const ad =
      this.config.ak + ' Headers=ctyun-eop-request-id;eop-date Signature=' + s;

    return {
      'Eop-date': dstr,
      'ctyun-eop-request-id': uid,
      'Eop-Authorization': ad,
      // 'User-Agent': 'Mozilla/5.0(jssdk)',
    };
  }
}
