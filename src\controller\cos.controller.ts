import { Controller, Inject, Get } from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { CosService } from '../service/cos.service';
import { CustomError } from '../error/custom.error';

@Controller('/cos')
export class UserController {
  @Inject()
  ctx: Context;

  @Inject()
  cosService: CosService;

  @Get('/authorization', {
    summary: '获取cos临时密钥',
  })
  async getCredential() {
    try {
      const authorization = await this.cosService.getCredential();
      return authorization;
    } catch (error) {
      throw new CustomError(error.message);
    }
  }
}
