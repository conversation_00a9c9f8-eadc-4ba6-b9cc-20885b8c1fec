// generateUniqueCode.js
import * as crypto from 'crypto';

// 定义可用字符集（数字和大写字母）
const CHARACTERS = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';
const CODE_LENGTH = 8;

// 生成单个随机字符
function getRandomChar() {
  const buffer = crypto.randomBytes(1);
  const randomIndex = buffer[0] % CHARACTERS.length;
  return CHARACTERS.charAt(randomIndex);
}

// 生成唯一随机码（基于时间戳和随机字符组合）
function generateUniqueCode() {
  // 获取当前时间戳（13位）
  const timestamp = Date.now().toString();
  // 截取后5位
  const timePart = timestamp.slice(-5);

  // 生成3位随机字符
  let randomPart = '';
  for (let i = 0; i < 3; i++) {
    randomPart += getRandomChar();
  }

  // 组合并打乱顺序
  let code = timePart + randomPart;
  code = code
    .split('')
    .sort(() => Math.random() - 0.5)
    .join('');

  // 确保长度为8位
  if (code.length > CODE_LENGTH) {
    code = code.slice(0, CODE_LENGTH);
  } else if (code.length < CODE_LENGTH) {
    while (code.length < CODE_LENGTH) {
      code += getRandomChar();
    }
    code = code
      .split('')
      .sort(() => Math.random() - 0.5)
      .join('');
  }

  return code;
}

// 验证码是否符合格式要求
function validateCode(code) {
  const regex = new RegExp(`^[${CHARACTERS}]{${CODE_LENGTH}}$`);
  return regex.test(code);
}

// 导出模块
export { generateUniqueCode, validateCode };
