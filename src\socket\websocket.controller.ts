import {
  W<PERSON><PERSON><PERSON>er,
  OnWSConnection,
  OnWSMessage,
  OnWSDisConnection,
  Inject,
  Logger,
  WSBroadCast,
} from '@midwayjs/core';
import { Context } from '@midwayjs/ws';
import { ILogger } from '@midwayjs/logger';
import * as http from 'http';
import { WebSocketService } from '../service/websocket.service';
import '../interface/websocket-extend';

/**
 * WebSocket 控制器
 * 处理 WebSocket 连接、消息和断开连接
 */
@WSController()
export class WebSocketController {
  @Inject()
  ctx: Context;

  @Inject()
  webSocketService: WebSocketService;

  @Logger()
  logger: ILogger;

  /**
   * 客户端连接时触发
   * @param socket WebSocket 上下文
   * @param request HTTP 请求对象
   */
  @OnWSConnection()
  async onConnection(socket: Context, request: http.IncomingMessage) {
    const clientId = this.generateClientId();
    const clientInfo = {
      id: clientId,
      ip: request.socket.remoteAddress,
      userAgent: request.headers['user-agent'],
      connectedAt: new Date(),
    };

    // 将客户端信息存储到上下文中
    this.ctx.clientInfo = clientInfo;

    this.logger.info(
      `WebSocket 客户端连接: ${clientId}, IP: ${clientInfo.ip}, 状态: ${this.ctx.readyState}`
    );

    // 向客户端发送连接成功消息
    this.ctx.send(
      JSON.stringify({
        type: 'connection',
        data: {
          clientId,
          message: '连接成功',
          timestamp: new Date().toISOString(),
        },
      })
    );

    // 通知其他客户端有新用户加入
    await this.webSocketService.broadcastToOthers(this.ctx, {
      type: 'user_joined',
      data: {
        clientId,
        message: `用户 ${clientId} 加入了聊天`,
        timestamp: new Date().toISOString(),
      },
    });
  }

  /**
   * 接收客户端消息
   * @param data 消息数据
   */
  @OnWSMessage('message')
  async onMessage(data: any) {
    try {
      let messageData;
      
      // 解析消息数据
      if (typeof data === 'string') {
        try {
          messageData = JSON.parse(data);
        } catch {
          messageData = { type: 'text', content: data };
        }
      } else {
        messageData = data;
      }

      const clientInfo = this.ctx.clientInfo;
      this.logger.info(
        `收到来自客户端 ${clientInfo?.id} 的消息:`,
        messageData
      );

      // 根据消息类型处理
      switch (messageData.type) {
        case 'chat':
          return await this.handleChatMessage(messageData);
        case 'ping':
          return await this.handlePingMessage();
        case 'join_room':
          return await this.handleJoinRoom(messageData);
        case 'leave_room':
          return await this.handleLeaveRoom(messageData);
        default:
          return await this.handleDefaultMessage(messageData);
      }
    } catch (error) {
      this.logger.error('处理WebSocket消息时发生错误:', error);
      return {
        type: 'error',
        message: '消息处理失败',
        timestamp: new Date().toISOString(),
      };
    }
  }

  /**
   * 处理聊天消息
   * @param messageData 消息数据
   */
  @WSBroadCast()
  async handleChatMessage(messageData: any) {
    const clientInfo = this.ctx.clientInfo;
    const response = {
      type: 'chat',
      data: {
        clientId: clientInfo?.id,
        content: messageData.content,
        timestamp: new Date().toISOString(),
      },
    };

    this.logger.info(`广播聊天消息: ${messageData.content}`);
    return response;
  }

  /**
   * 处理心跳消息
   */
  async handlePingMessage() {
    return {
      type: 'pong',
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * 处理加入房间
   * @param messageData 消息数据
   */
  async handleJoinRoom(messageData: any) {
    const { roomId } = messageData;
    const clientInfo = this.ctx.clientInfo;
    
    // 这里可以实现房间逻辑
    this.logger.info(`客户端 ${clientInfo?.id} 加入房间: ${roomId}`);
    
    return {
      type: 'room_joined',
      data: {
        roomId,
        message: `成功加入房间 ${roomId}`,
        timestamp: new Date().toISOString(),
      },
    };
  }

  /**
   * 处理离开房间
   * @param messageData 消息数据
   */
  async handleLeaveRoom(messageData: any) {
    const { roomId } = messageData;
    const clientInfo = this.ctx.clientInfo;
    
    this.logger.info(`客户端 ${clientInfo?.id} 离开房间: ${roomId}`);
    
    return {
      type: 'room_left',
      data: {
        roomId,
        message: `已离开房间 ${roomId}`,
        timestamp: new Date().toISOString(),
      },
    };
  }

  /**
   * 处理默认消息
   * @param messageData 消息数据
   */
  async handleDefaultMessage(messageData: any) {
    const clientInfo = this.ctx.clientInfo;
    return {
      type: 'echo',
      data: {
        clientId: clientInfo?.id,
        originalMessage: messageData,
        message: '消息已收到',
        timestamp: new Date().toISOString(),
      },
    };
  }

  /**
   * 客户端断开连接时触发
   */
  @OnWSDisConnection()
  async onDisconnection() {
    const clientInfo = this.ctx.clientInfo;
    
    if (clientInfo) {
      this.logger.info(`WebSocket 客户端断开连接: ${clientInfo.id}`);
      
      // 通知其他客户端用户离开
      await this.webSocketService.broadcastToOthers(this.ctx, {
        type: 'user_left',
        data: {
          clientId: clientInfo.id,
          message: `用户 ${clientInfo.id} 离开了聊天`,
          timestamp: new Date().toISOString(),
        },
      });
    }
  }

  /**
   * 生成客户端ID
   */
  private generateClientId(): string {
    return `client_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}
