import {
  Table,
  Column,
  Model,
  DataType,
  BeforeCreate,
  BeforeUpdate,
  BelongsToMany,
} from 'sequelize-typescript';
import * as bcrypt from 'bcryptjs';
import { Role } from './role.entity';
import { UserRole } from './user_role.entity';

export interface UserAttributes {
  /** 用户ID */
  id: number;
  /** 用户名 */
  username: string;
  /** 密码 */
  password: string;
  /** 昵称 */
  nickname?: string;
  /** 头像 */
  avatar?: string;
  /** 邮箱 */
  email?: string;
  /** 是否激活 */
  isActive?: boolean;
  /** 用户身份 */
  roles?: Role[];
}

@Table({ tableName: 'users', timestamps: true, comment: '用户表' })
export class User extends Model<UserAttributes> implements UserAttributes {
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  })
  id: number;

  @Column({
    type: DataType.STRING,
    unique: {
      name: 'username',
      msg: '已存在相同的用户名',
    },
    allowNull: false,
    comment: '用户名',
  })
  username: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
    comment: '密码',
  })
  password: string;

  @Column({
    type: DataType.STRING,
    comment: '昵称',
  })
  nickname: string;

  @Column({
    type: DataType.STRING,
    comment: '头像',
  })
  avatar: string;

  @Column({
    type: DataType.STRING,
    comment: '邮箱',
  })
  email: string;

  @Column({
    type: DataType.BOOLEAN,
    defaultValue: true,
    comment: '是否激活',
  })
  isActive: boolean;

  @BelongsToMany(() => Role, () => UserRole)
  roles: Role[];

  @BeforeCreate
  static async hashPassword(instance: User) {
    instance.password = await bcrypt.hash(instance.password, 10);
  }

  @BeforeUpdate
  static async updatePassword(instance: User) {
    if (instance.changed('password')) {
      instance.password = await bcrypt.hash(instance.password, 10);
    }
  }

  async validatePassword(password: string) {
    return bcrypt.compare(password, this.password);
  }
}
