import { Controller, Get, Inject, Query } from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { WeappService } from '../service/weapp.service';

@Controller('/dev-tools')
export class DevToolsController {
  @Inject()
  ctx: Context;

  @Inject()
  weappService: WeappService;

  @Get('/send-order-confirmation-message', { summary: '发送接单成功提醒消息' })
  async sendOrderConfirmationMessage(@Query() query: any) {
    const { sn } = query;
    return this.weappService.sendOrderConfirmationMessage(sn as string);
  }
}
