import { Config, Inject, InjectClient, Provide } from '@midwayjs/core';
import { SignParams, WePay } from '../common/wePay';
import { Customer, Order, OrderDetail, ServiceChangeLog } from '../entity';
import {
  AxiosResponse,
  HttpService,
  HttpServiceFactory,
} from '@midwayjs/axios';
import { Context } from '@midwayjs/koa';
import { CustomError } from '../error/custom.error';
import { OrderStatus, OrderStatusChangeType } from '../common/Constant';
import {
  MembershipCardOrder,
  MembershipCardOrderStatus,
} from '../entity/membership-card-order.entity';
import { CouponOrder, CouponOrderStatus } from '../entity/coupon-order.entity';
import { OrderService } from './order.service';

@Provide()
export class WepayService {
  @Inject()
  ctx: Context;

  @Inject()
  wePay: WePay;

  @Config('weapp')
  weappConfig: {
    appid: string;
    secret: string;
    sym_sn: string;
    sym_key: string;
  };

  @Config('wePay')
  wePayConfig: {
    sp_mchid: string;
    sp_appid: string;
    pem_sn: string;
  };

  @InjectClient(HttpServiceFactory, 'weapp_pay')
  weappPayService: HttpService;

  @Inject()
  orderService: OrderService;

  private analisisRes(res: AxiosResponse) {
    if (res.status === 200) {
      return res.data;
    }
    return {};
  }

  /** JSAPI/小程序下单 */
  async jsapi(appid: string, sn: string) {
    // 判断是服务订单、权益卡订单还是代金券订单
    if (sn.startsWith('MC')) {
      return await this.jsapiForMembershipCard(appid, sn);
    } else if (sn.startsWith('CP')) {
      return await this.jsapiForCoupon(appid, sn);
    } else {
      return await this.jsapiForService(appid, sn);
    }
  }

  /** 服务订单JSAPI/小程序下单 */
  async jsapiForService(appid: string, sn: string) {
    const order = await Order.findOne({
      where: {
        sn,
      },
      include: [
        {
          model: OrderDetail,
        },
        {
          model: Customer,
        },
      ],
    });
    if (!order) {
      throw new CustomError('订单不存在');
    }
    if (order.status !== OrderStatus.待付款) {
      throw new CustomError('订单状态不正确');
    }
    if (order.prepay_id) {
      return { prepay_id: order.prepay_id };
    }
    // console.log('order', order);
    // 支付结束时间，15分钟过期
    const date = new Date(Date.now() + 1000 * 60 * 15);
    const time_expire = date.toISOString().replace('Z', '+08:00');
    console.log('time_expire: ', time_expire);
    const body = {
      appid,
      mchid: this.wePayConfig.sp_mchid,
      description: '贝宠服务订单支付',
      out_trade_no: order.sn,
      time_expire,
      // attach: String(order.id),
      notify_url: 'https://manager.petsjoylife.com/api/openapi/pay/callback',
      amount: {
        total: Math.round(order.totalFee * 100),
        currency: 'CNY',
      },
      payer: {
        openid: order.customer.openid,
      },
    };
    console.log('body: ', body);
    const requestInfo = {
      // url: '/v3/pay/partner/transactions/jsapi',
      url: '/v3/pay/transactions/jsapi',
      body,
    };
    this.ctx.logger.info('【发起付款】：', requestInfo);
    try {
      if (requestInfo.body.amount.total > 0) {
        const signature = this.wePay.genSign({
          method: 'POST',
          url: requestInfo.url,
          body: requestInfo.body,
        });
        const res = await this.weappPayService.post(requestInfo.url, body, {
          headers: {
            Authorization: signature,
            Accept: 'application/json',
            'Content-Type': 'application/json',
          },
        });
        this.ctx.logger.info('【发起付款结果】：', {
          status: res.status,
          data: res.data,
        });
        const data = this.analisisRes(res);
        // console.log('data: ', data);
        if (data.prepay_id) {
          await order.update({
            prepay_id: data.prepay_id,
          });
        }
        return data;
      }
      // 0元订单直接返回成功，这里只是支付前的动作，所以不需要更新订单状态，也不写日志
      return '0';
    } catch (error) {
      if (error.response?.data?.message === '该订单已支付') {
        console.log(error.response.data.message);
        this.ctx.logger.info('【订单已支付】：', order);
        await order.update({
          status: OrderStatus.待接单,
        });
        await ServiceChangeLog.create({
          orderId: order.id,
          changeType: OrderStatusChangeType.付款,
          description: '订单已支付，由系统自动更新订单状态',
        });

        // 广播新订单消息
        await this.orderService.broadcastNewOrder(order);

        return '订单已支付，由系统自动更新订单状态';
      }
      this.ctx.logger.error('【发起付款错误】：', error.response);
      throw error;
    }
  }

  /** 权益卡订单JSAPI/小程序下单 */
  async jsapiForMembershipCard(appid: string, sn: string) {
    const order = await MembershipCardOrder.findOne({
      where: {
        sn,
      },
      include: [
        {
          model: Customer,
        },
      ],
    });
    if (!order) {
      throw new CustomError('订单不存在');
    }
    if (order.status !== MembershipCardOrderStatus.PENDING_PAYMENT) {
      throw new CustomError('订单状态不正确');
    }
    if (order.prepayId) {
      return { prepay_id: order.prepayId };
    }
    // 支付结束时间，15分钟过期
    const date = new Date(Date.now() + 1000 * 60 * 15);
    const time_expire = date.toISOString().replace('Z', '+08:00');
    console.log('time_expire: ', time_expire);
    const body = {
      appid,
      mchid: this.wePayConfig.sp_mchid,
      description: '贝宠权益卡购买',
      out_trade_no: order.sn,
      time_expire,
      notify_url: 'https://manager.petsjoylife.com/api/openapi/pay/callback',
      amount: {
        total: Math.round(order.amount * 100), // 转换为分
        currency: 'CNY',
      },
      payer: {
        openid: order.customer.openid,
      },
    };
    console.log('body: ', body);
    const requestInfo = {
      url: '/v3/pay/transactions/jsapi',
      body,
    };
    this.ctx.logger.info('【发起权益卡付款】：', requestInfo);
    const signature = this.wePay.genSign({
      method: 'POST',
      url: requestInfo.url,
      body: requestInfo.body,
    });
    try {
      const res = await this.weappPayService.post(requestInfo.url, body, {
        headers: {
          Authorization: signature,
          Accept: 'application/json',
          'Content-Type': 'application/json',
        },
      });
      this.ctx.logger.info('【发起权益卡付款结果】：', {
        status: res.status,
        data: res.data,
      });
      const data = this.analisisRes(res);
      if (data.prepay_id) {
        await order.update({
          prepayId: data.prepay_id,
        });
      }
      return data;
    } catch (error) {
      if (error.response?.data?.message === '该订单已支付') {
        console.log(error.response.data.message);
        this.ctx.logger.info('【权益卡订单已支付】：', order);
        await order.update({
          status: MembershipCardOrderStatus.PAID,
          payTime: new Date(),
        });
        return '订单已支付，由系统自动更新订单状态';
      }
      this.ctx.logger.error('【发起权益卡付款错误】：', error.response);
      throw error;
    }
  }

  /** 代金券订单JSAPI/小程序下单 */
  async jsapiForCoupon(appid: string, sn: string) {
    const order = await CouponOrder.findOne({
      where: {
        sn,
      },
      include: [
        {
          model: Customer,
        },
      ],
    });
    if (!order) {
      throw new CustomError('订单不存在');
    }
    if (order.status !== CouponOrderStatus.PENDING_PAYMENT) {
      throw new CustomError('订单状态不正确');
    }
    if (order.prepayId) {
      return { prepay_id: order.prepayId };
    }
    // 支付结束时间，15分钟过期
    const date = new Date(Date.now() + 1000 * 60 * 15);
    const time_expire = date.toISOString().replace('Z', '+08:00');
    console.log('time_expire: ', time_expire);
    const body = {
      appid,
      mchid: this.wePayConfig.sp_mchid,
      description: '贝宠代金券购买',
      out_trade_no: order.sn,
      time_expire,
      notify_url: 'https://manager.petsjoylife.com/api/openapi/pay/callback',
      amount: {
        total: Math.round(order.amount * 100), // 转换为分
        currency: 'CNY',
      },
      payer: {
        openid: order.customer.openid,
      },
    };
    console.log('body: ', body);
    const requestInfo = {
      url: '/v3/pay/transactions/jsapi',
      body,
    };
    this.ctx.logger.info('【发起代金券付款】：', requestInfo);
    const signature = this.wePay.genSign({
      method: 'POST',
      url: requestInfo.url,
      body: requestInfo.body,
    });
    try {
      const res = await this.weappPayService.post(requestInfo.url, body, {
        headers: {
          Authorization: signature,
          Accept: 'application/json',
          'Content-Type': 'application/json',
        },
      });
      this.ctx.logger.info('【发起代金券付款结果】：', {
        status: res.status,
        data: res.data,
      });
      const data = this.analisisRes(res);
      if (data.prepay_id) {
        await order.update({
          prepayId: data.prepay_id,
        });
      }
      return data;
    } catch (error) {
      if (error.response?.data?.message === '该订单已支付') {
        console.log(error.response.data.message);
        this.ctx.logger.info('【代金券订单已支付】：', order);
        await order.update({
          status: CouponOrderStatus.PAID,
          payTime: new Date(),
        });
        return '订单已支付，由系统自动更新订单状态';
      }
      this.ctx.logger.error('【发起代金券付款错误】：', error.response);
      throw error;
    }
  }

  /** 微信支付订单号查询订单 */
  async getTransactionsBySN(sn: string) {
    const requestInfo = {
      url: `/v3/pay/transactions/out-trade-no/${sn}`,
      query: { mchid: this.wePayConfig.sp_mchid },
    };
    console.log('requestInfo: ', requestInfo);
    const signature = this.wePay.genSign({
      method: 'GET',
      url: requestInfo.url,
      query: requestInfo.query,
    });
    try {
      const res = await this.weappPayService.get(requestInfo.url, {
        params: requestInfo.query,
        headers: {
          Authorization: signature,
          Accept: 'application/json',
          'Content-Type': 'application/json',
        },
      });
      const data = this.analisisRes(res);
      return data;
    } catch (error) {
      this.ctx.logger.error('【微信支付订单号查询订单】错误：', error.response);
      throw error;
    }
  }

  /**
   * 退款
   *
   * @param {string} sn 订单号
   * @param {number} [refund] 退款金额，可选，单位为元
   */
  async refund(sn: string, refund?: number) {
    const order = await Order.findOne({
      where: {
        sn,
      },
      attributes: ['id', 'sn', 'totalFee', 'status'],
    });
    if (!order) {
      throw new CustomError('订单不存在');
    }
    if (
      !([OrderStatus.待接单, OrderStatus.退款中] as string[]).includes(
        order.status
      )
    ) {
      throw new CustomError('自动退款仅支持已付款且未接单订单的退款');
    }
    const options: SignParams = {
      url: '/v3/refund/domestic/refunds',
      method: 'POST',
      body: {
        out_trade_no: sn,
        out_refund_no: sn,
        reason: '用户申请退款',
        amount: {
          refund: (refund || order.totalFee) * 100,
          total: order.totalFee * 100,
          currency: 'CNY',
        },
      },
    };
    this.ctx.logger.info('【微信支付退款】：', options.body);
    try {
      if (options.body.amount.refund > 0) {
        const signature = this.wePay.genSign({
          method: options.method,
          url: options.url,
          body: options.body,
        });
        const res = await this.weappPayService.post(options.url, options.body, {
          headers: {
            Authorization: signature,
            Accept: 'application/json',
            'Content-Type': 'application/json',
          },
        });
        const data = this.analisisRes(res);
        if (data.refund_id) {
          await order.update({
            status: OrderStatus.已退款,
          });
          await ServiceChangeLog.create({
            orderId: order.id,
            changeType: OrderStatusChangeType.退款,
            description: '订单已退款，由系统自动更新订单状态',
          });
        }
        return data;
      }
      // 0元订单直接返回成功
      await order.update({
        status: OrderStatus.已退款,
      });
      await ServiceChangeLog.create({
        orderId: order.id,
        changeType: OrderStatusChangeType.退款,
        description: '订单已退款，由系统自动更新订单状态',
      });
      return '0';
    } catch (error) {
      this.ctx.logger.error('【微信支付退款】错误：', error.response);
      throw error;
    }
  }
}
