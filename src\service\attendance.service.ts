import { Provide } from '@midwayjs/core';
import { Attendance } from '../entity/attendance.entity';
import { BaseService } from '../common/BaseService';
import { Employee } from '../entity/employee.entity';
import { Vehicle } from '../entity/vehicle.entity';
import { Op } from 'sequelize';

@Provide()
export class AttendanceService extends BaseService<Attendance> {
  constructor() {
    super('考勤');
  }

  getModel() {
    return Attendance;
  }

  async findByEmployee(employeeId: number) {
    return await this.findAll({
      query: { employeeId },
      include: [Employee, Vehicle],
    });
  }

  async findByDate(startDate: Date, endDate: Date) {
    return await this.findAll({
      query: {
        checkInTime: {
          [Op.between]: [startDate, endDate],
        },
      },
      include: [Employee, Vehicle],
    });
  }
}
