<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket 测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 10px;
            font-weight: bold;
        }
        .status.connected {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.disconnected {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.connecting {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        button {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        .btn-danger {
            background-color: #dc3545;
            color: white;
        }
        .btn-warning {
            background-color: #ffc107;
            color: black;
        }
        button:hover {
            opacity: 0.8;
        }
        button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        .message-area {
            display: flex;
            gap: 20px;
        }
        .message-input, .message-log {
            flex: 1;
        }
        .message-log {
            height: 400px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            background-color: #f8f9fa;
            font-family: monospace;
            font-size: 12px;
        }
        .message-item {
            margin-bottom: 8px;
            padding: 5px;
            border-radius: 3px;
        }
        .message-sent {
            background-color: #e3f2fd;
            border-left: 3px solid #2196f3;
        }
        .message-received {
            background-color: #f3e5f5;
            border-left: 3px solid #9c27b0;
        }
        .message-system {
            background-color: #fff3e0;
            border-left: 3px solid #ff9800;
        }
        .message-error {
            background-color: #ffebee;
            border-left: 3px solid #f44336;
        }
        input, textarea, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-bottom: 10px;
            box-sizing: border-box;
        }
        textarea {
            height: 100px;
            resize: vertical;
        }
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        .info-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            border: 1px solid #e9ecef;
        }
        .info-card h4 {
            margin: 0 0 10px 0;
            color: #495057;
        }
        .info-card p {
            margin: 5px 0;
            font-size: 14px;
        }
        .timestamp {
            color: #6c757d;
            font-size: 11px;
        }
    </style>
</head>
<body>
    <h1>WebSocket 测试页面</h1>
    
    <div class="container">
        <h2>连接状态</h2>
        <div id="status" class="status disconnected">未连接</div>
        
        <div class="controls">
            <button id="connectBtn" class="btn-primary">连接</button>
            <button id="disconnectBtn" class="btn-danger" disabled>断开连接</button>
            <button id="clearLogBtn" class="btn-warning">清空日志</button>
            <button id="pingBtn" class="btn-success" disabled>发送心跳</button>
        </div>

        <div class="info-grid">
            <div class="info-card">
                <h4>连接信息</h4>
                <p>服务器: <span id="serverUrl">ws://localhost:3001</span></p>
                <p>客户端ID: <span id="clientId">-</span></p>
                <p>连接时间: <span id="connectTime">-</span></p>
            </div>
            <div class="info-card">
                <h4>消息统计</h4>
                <p>发送: <span id="sentCount">0</span></p>
                <p>接收: <span id="receivedCount">0</span></p>
                <p>错误: <span id="errorCount">0</span></p>
            </div>
        </div>
    </div>

    <div class="container">
        <h2>消息发送</h2>
        <div class="message-area">
            <div class="message-input">
                <h3>发送消息</h3>
                <select id="messageType">
                    <option value="chat">聊天消息</option>
                    <option value="ping">心跳消息</option>
                    <option value="join_room">加入房间</option>
                    <option value="leave_room">离开房间</option>
                    <option value="custom">自定义</option>
                </select>
                <textarea id="messageContent" placeholder="输入消息内容..."></textarea>
                <input type="text" id="roomId" placeholder="房间ID (可选)" style="display:none;">
                <button id="sendBtn" class="btn-primary" disabled>发送消息</button>
                
                <h4>快速消息</h4>
                <div class="controls">
                    <button class="btn-success quick-msg" data-msg='{"type":"chat","content":"Hello World!"}'>Hello</button>
                    <button class="btn-success quick-msg" data-msg='{"type":"ping"}'>Ping</button>
                    <button class="btn-success quick-msg" data-msg='{"type":"join_room","roomId":"room1"}'>加入房间1</button>
                    <button class="btn-success quick-msg" data-msg='{"type":"leave_room","roomId":"room1"}'>离开房间1</button>
                </div>
            </div>
            
            <div class="message-log">
                <h3>消息日志</h3>
                <div id="messageLog"></div>
            </div>
        </div>
    </div>

    <script>
        let ws = null;
        let clientId = null;
        let sentCount = 0;
        let receivedCount = 0;
        let errorCount = 0;

        const statusEl = document.getElementById('status');
        const connectBtn = document.getElementById('connectBtn');
        const disconnectBtn = document.getElementById('disconnectBtn');
        const clearLogBtn = document.getElementById('clearLogBtn');
        const pingBtn = document.getElementById('pingBtn');
        const sendBtn = document.getElementById('sendBtn');
        const messageTypeEl = document.getElementById('messageType');
        const messageContentEl = document.getElementById('messageContent');
        const roomIdEl = document.getElementById('roomId');
        const messageLogEl = document.getElementById('messageLog');
        const clientIdEl = document.getElementById('clientId');
        const connectTimeEl = document.getElementById('connectTime');
        const sentCountEl = document.getElementById('sentCount');
        const receivedCountEl = document.getElementById('receivedCount');
        const errorCountEl = document.getElementById('errorCount');

        function updateStatus(status, message) {
            statusEl.className = `status ${status}`;
            statusEl.textContent = message;
        }

        function updateStats() {
            sentCountEl.textContent = sentCount;
            receivedCountEl.textContent = receivedCount;
            errorCountEl.textContent = errorCount;
        }

        function addLogMessage(message, type = 'system') {
            const messageEl = document.createElement('div');
            messageEl.className = `message-item message-${type}`;
            
            const timestamp = new Date().toLocaleTimeString();
            messageEl.innerHTML = `
                <div class="timestamp">[${timestamp}]</div>
                <div>${typeof message === 'object' ? JSON.stringify(message, null, 2) : message}</div>
            `;
            
            messageLogEl.appendChild(messageEl);
            messageLogEl.scrollTop = messageLogEl.scrollHeight;
        }

        function connect() {
            if (ws && ws.readyState === WebSocket.OPEN) {
                return;
            }

            updateStatus('connecting', '正在连接...');
            
            const serverUrl = document.getElementById('serverUrl').textContent;
            ws = new WebSocket(serverUrl);

            ws.onopen = function(event) {
                updateStatus('connected', '已连接');
                connectBtn.disabled = true;
                disconnectBtn.disabled = false;
                sendBtn.disabled = false;
                pingBtn.disabled = false;
                connectTimeEl.textContent = new Date().toLocaleString();
                addLogMessage('WebSocket 连接已建立', 'system');
            };

            ws.onmessage = function(event) {
                receivedCount++;
                updateStats();
                
                try {
                    const data = JSON.parse(event.data);
                    addLogMessage(data, 'received');
                    
                    if (data.type === 'connection' && data.data.clientId) {
                        clientId = data.data.clientId;
                        clientIdEl.textContent = clientId;
                    }
                } catch (e) {
                    addLogMessage(event.data, 'received');
                }
            };

            ws.onclose = function(event) {
                updateStatus('disconnected', '连接已断开');
                connectBtn.disabled = false;
                disconnectBtn.disabled = true;
                sendBtn.disabled = true;
                pingBtn.disabled = true;
                addLogMessage(`连接关闭: ${event.code} - ${event.reason}`, 'system');
            };

            ws.onerror = function(error) {
                errorCount++;
                updateStats();
                addLogMessage(`连接错误: ${error}`, 'error');
            };
        }

        function disconnect() {
            if (ws) {
                ws.close();
            }
        }

        function sendMessage() {
            if (!ws || ws.readyState !== WebSocket.OPEN) {
                addLogMessage('WebSocket 未连接', 'error');
                return;
            }

            const type = messageTypeEl.value;
            const content = messageContentEl.value.trim();
            const roomId = roomIdEl.value.trim();

            let message;
            
            if (type === 'custom') {
                try {
                    message = JSON.parse(content);
                } catch (e) {
                    addLogMessage('自定义消息格式错误，请输入有效的JSON', 'error');
                    return;
                }
            } else {
                message = { type };
                
                if (type === 'chat') {
                    message.content = content;
                } else if (type === 'join_room' || type === 'leave_room') {
                    message.roomId = roomId || 'default';
                }
            }

            ws.send(JSON.stringify(message));
            sentCount++;
            updateStats();
            addLogMessage(message, 'sent');
            
            if (type !== 'custom') {
                messageContentEl.value = '';
            }
        }

        // 事件监听器
        connectBtn.addEventListener('click', connect);
        disconnectBtn.addEventListener('click', disconnect);
        clearLogBtn.addEventListener('click', () => {
            messageLogEl.innerHTML = '';
            sentCount = receivedCount = errorCount = 0;
            updateStats();
        });
        pingBtn.addEventListener('click', () => {
            if (ws && ws.readyState === WebSocket.OPEN) {
                const pingMessage = { type: 'ping' };
                ws.send(JSON.stringify(pingMessage));
                sentCount++;
                updateStats();
                addLogMessage(pingMessage, 'sent');
            }
        });
        sendBtn.addEventListener('click', sendMessage);

        messageTypeEl.addEventListener('change', function() {
            const isRoomMessage = this.value === 'join_room' || this.value === 'leave_room';
            roomIdEl.style.display = isRoomMessage ? 'block' : 'none';
            
            if (this.value === 'ping') {
                messageContentEl.style.display = 'none';
            } else {
                messageContentEl.style.display = 'block';
            }
        });

        messageContentEl.addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && e.ctrlKey) {
                sendMessage();
            }
        });

        // 快速消息按钮
        document.querySelectorAll('.quick-msg').forEach(btn => {
            btn.addEventListener('click', function() {
                if (ws && ws.readyState === WebSocket.OPEN) {
                    const message = JSON.parse(this.dataset.msg);
                    ws.send(JSON.stringify(message));
                    sentCount++;
                    updateStats();
                    addLogMessage(message, 'sent');
                }
            });
        });

        // 页面加载完成后自动连接
        window.addEventListener('load', function() {
            addLogMessage('页面加载完成，点击"连接"按钮开始测试', 'system');
        });
    </script>
</body>
</html>
