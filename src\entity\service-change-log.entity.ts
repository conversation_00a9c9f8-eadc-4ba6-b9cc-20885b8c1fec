import {
  Table,
  Column,
  Model,
  DataType,
  BelongsTo,
  ForeignKey,
} from 'sequelize-typescript';
import { Order } from './order.entity';
import { Customer } from './customer.entity';
import { Employee } from './employee.entity';

export interface ServiceChangeLogAttributes {
  /** 变更记录ID */
  id: number;
  /** 关联订单ID */
  orderId: number;
  /** 变更类型 */
  changeType: string;
  /** 用户端发起人 */
  customerId?: number;
  /** 员工端发起人 */
  employeeId?: number;
  /** 描述 */
  description?: string;
  /** 关联的订单信息 */
  order?: Order;
  /** 用户端发起人信息 */
  customer?: Customer;
  /** 员工端发起人信息 */
  employee?: Employee;
}

@Table({
  tableName: 'service_change_logs',
  timestamps: true,
  comment: '订单服务变更记录表',
})
export class ServiceChangeLog
  extends Model<ServiceChangeLogAttributes>
  implements ServiceChangeLogAttributes
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '变更记录ID',
  })
  id: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '关联订单ID',
  })
  @ForeignKey(() => Order)
  orderId: number;

  @Column({
    type: DataType.STRING(10),
    allowNull: false,
    comment: '变更类型',
  })
  changeType: string;

  /** 用户端发起人 */
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    comment: '用户端发起人',
  })
  @ForeignKey(() => Customer)
  customerId?: number;

  /** 员工端发起人 */
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    comment: '员工端发起人',
  })
  @ForeignKey(() => Employee)
  employeeId?: number;

  /** 描述 */
  @Column({
    type: DataType.STRING(255),
    allowNull: true,
    comment: '描述',
  })
  description?: string;

  @BelongsTo(() => Order)
  order: Order;

  @BelongsTo(() => Customer)
  customer: Customer;

  @BelongsTo(() => Employee)
  employee: Employee;
}
