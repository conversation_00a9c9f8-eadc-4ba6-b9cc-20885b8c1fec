import { Controller, Get, Post, Body, Inject } from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { WebSocketService } from '../service/websocket.service';

/**
 * 简单的 WebSocket 管理控制器
 */
@Controller('/api/websocket')
export class WebSocketAdminController {
  @Inject()
  ctx: Context;

  @Inject()
  webSocketService: WebSocketService;

  /**
   * 获取 WebSocket 服务状态
   */
  @Get('/status', { summary: '获取WebSocket服务状态' })
  async getStatus() {
    const clientCount = this.webSocketService.getConnectedClientCount();
    const clients = this.webSocketService.getConnectedClients();

    return {
      success: true,
      data: {
        isRunning: true,
        connectedClientCount: clientCount,
        connectedClients: clients,
        serverTime: Date.now(),
      },
    };
  }

  /**
   * 向所有客户端广播消息
   */
  @Post('/broadcast', { summary: '向所有客户端广播消息' })
  async broadcast(@Body() body: { message: string }) {
    const { message } = body;

    if (!message) {
      return {
        success: false,
        message: '消息内容不能为空',
      };
    }

    const broadcastMessage = {
      type: 'broadcast',
      content: message,
      timestamp: Date.now(),
      from: 'admin',
    };

    const clientCount = await this.webSocketService.broadcastToAll(broadcastMessage);

    return {
      success: true,
      data: {
        message: '消息广播成功',
        clientCount,
      },
    };
  }

  /**
   * 健康检查接口
   */
  @Get('/health', { summary: 'WebSocket服务健康检查' })
  async healthCheck() {
    try {
      const clientCount = this.webSocketService.getConnectedClientCount();

      return {
        success: true,
        data: {
          status: 'healthy',
          connectedClients: clientCount,
          timestamp: Date.now(),
          uptime: process.uptime(),
        },
      };
    } catch (error) {
      return {
        success: false,
        data: {
          status: 'unhealthy',
          error: error.message,
          timestamp: Date.now(),
        },
      };
    }
  }
}
