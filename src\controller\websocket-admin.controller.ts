import { Controller, Get, Post, Body, Query, Inject } from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { WebSocketService } from '../service/websocket.service';
import { IWebSocketStats } from '../interface/websocket.interface';

/**
 * WebSocket 管理控制器
 * 提供 HTTP 接口来管理和监控 WebSocket 服务
 */
@Controller('/api/websocket')
export class WebSocketAdminController {
  @Inject()
  ctx: Context;

  @Inject()
  webSocketService: WebSocketService;

  /**
   * 获取 WebSocket 服务状态
   */
  @Get('/status', { summary: '获取WebSocket服务状态' })
  async getStatus() {
    const connectedClients = this.webSocketService.getConnectedClients();
    const clientCount = this.webSocketService.getConnectedClientCount();

    return {
      success: true,
      data: {
        isRunning: true,
        connectedClientCount: clientCount,
        connectedClients: connectedClients,
        serverTime: new Date().toISOString(),
      },
    };
  }

  /**
   * 获取所有连接的客户端信息
   */
  @Get('/clients', { summary: '获取所有连接的客户端信息' })
  async getClients() {
    const clients = this.webSocketService.getConnectedClients();
    const clientCount = this.webSocketService.getConnectedClientCount();

    return {
      success: true,
      data: {
        total: clientCount,
        clients: clients,
      },
    };
  }

  /**
   * 向所有客户端广播消息
   */
  @Post('/broadcast', { summary: '向所有客户端广播消息' })
  async broadcast(@Body() body: { message: string; type?: string }) {
    const { message, type = 'system_notification' } = body;

    if (!message) {
      return {
        success: false,
        message: '消息内容不能为空',
      };
    }

    const broadcastMessage = {
      type,
      data: {
        message,
        timestamp: new Date().toISOString(),
        from: 'admin',
      },
    };

    const clientCount = await this.webSocketService.broadcastToAll(broadcastMessage);

    return {
      success: true,
      data: {
        message: '消息广播成功',
        clientCount,
        broadcastMessage,
      },
    };
  }

  /**
   * 向指定客户端发送消息
   */
  @Post('/send', { summary: '向指定客户端发送消息' })
  async sendToClient(
    @Body() body: { clientId: string; message: string; type?: string }
  ) {
    const { clientId, message, type = 'system_notification' } = body;

    if (!clientId || !message) {
      return {
        success: false,
        message: '客户端ID和消息内容不能为空',
      };
    }

    const messageData = {
      type,
      data: {
        message,
        timestamp: new Date().toISOString(),
        from: 'admin',
      },
    };

    const sent = await this.webSocketService.sendToClient(clientId, messageData);

    return {
      success: sent,
      data: {
        message: sent ? '消息发送成功' : '客户端未找到或未连接',
        clientId,
        messageData,
      },
    };
  }

  /**
   * 断开指定客户端连接
   */
  @Post('/disconnect', { summary: '断开指定客户端连接' })
  async disconnectClient(
    @Body() body: { clientId: string; reason?: string }
  ) {
    const { clientId, reason = '管理员操作' } = body;

    if (!clientId) {
      return {
        success: false,
        message: '客户端ID不能为空',
      };
    }

    const disconnected = await this.webSocketService.disconnectClient(
      clientId,
      reason
    );

    return {
      success: disconnected,
      data: {
        message: disconnected ? '客户端连接已断开' : '客户端未找到',
        clientId,
        reason,
      },
    };
  }

  /**
   * 发送系统通知
   */
  @Post('/notification', { summary: '发送系统通知' })
  async sendNotification(
    @Body() body: { 
      message: string; 
      level?: 'info' | 'warning' | 'error' | 'success';
      targetClientId?: string;
    }
  ) {
    const { message, level = 'info', targetClientId } = body;

    if (!message) {
      return {
        success: false,
        message: '通知内容不能为空',
      };
    }

    const notification = {
      type: 'system_notification',
      data: {
        message,
        level,
        timestamp: new Date().toISOString(),
        from: 'system',
      },
    };

    let result;
    if (targetClientId) {
      result = await this.webSocketService.sendToClient(targetClientId, notification);
    } else {
      result = await this.webSocketService.broadcastToAll(notification);
    }

    return {
      success: true,
      data: {
        message: targetClientId ? '通知发送成功' : '通知广播成功',
        notification,
        targetClientId,
        result,
      },
    };
  }

  /**
   * 获取房间信息
   */
  @Get('/rooms/:roomId', { summary: '获取指定房间信息' })
  async getRoomInfo(@Query('roomId') roomId: string) {
    if (!roomId) {
      return {
        success: false,
        message: '房间ID不能为空',
      };
    }

    const clients = this.webSocketService.getRoomClients(roomId);

    return {
      success: true,
      data: {
        roomId,
        memberCount: clients.length,
        members: clients,
      },
    };
  }

  /**
   * 向房间发送消息
   */
  @Post('/rooms/:roomId/send', { summary: '向指定房间发送消息' })
  async sendToRoom(
    @Query('roomId') roomId: string,
    @Body() body: { message: string; type?: string }
  ) {
    const { message, type = 'room_message' } = body;

    if (!roomId || !message) {
      return {
        success: false,
        message: '房间ID和消息内容不能为空',
      };
    }

    const messageData = {
      type,
      data: {
        roomId,
        message,
        timestamp: new Date().toISOString(),
        from: 'admin',
      },
    };

    const clientCount = await this.webSocketService.sendToRoom(roomId, messageData);

    return {
      success: true,
      data: {
        message: '房间消息发送成功',
        roomId,
        clientCount,
        messageData,
      },
    };
  }

  /**
   * 获取 WebSocket 统计信息
   */
  @Get('/stats', { summary: '获取WebSocket统计信息' })
  async getStats() {
    const connectedClients = this.webSocketService.getConnectedClientCount();
    const clients = this.webSocketService.getConnectedClients();

    const stats: Partial<IWebSocketStats> = {
      connectedClients,
      serverStartTime: new Date(), // 这里应该从实际的服务启动时间获取
      totalConnections: connectedClients, // 这里应该从持久化存储获取历史数据
      messagesSent: 0, // 这里应该从统计服务获取
      messagesReceived: 0, // 这里应该从统计服务获取
      roomCount: 0, // 这里应该计算实际房间数量
    };

    return {
      success: true,
      data: {
        stats,
        clientDetails: clients,
        timestamp: new Date().toISOString(),
      },
    };
  }

  /**
   * 健康检查接口
   */
  @Get('/health', { summary: 'WebSocket服务健康检查' })
  async healthCheck() {
    try {
      const clientCount = this.webSocketService.getConnectedClientCount();
      
      return {
        success: true,
        data: {
          status: 'healthy',
          connectedClients: clientCount,
          timestamp: new Date().toISOString(),
          uptime: process.uptime(),
        },
      };
    } catch (error) {
      return {
        success: false,
        data: {
          status: 'unhealthy',
          error: error.message,
          timestamp: new Date().toISOString(),
        },
      };
    }
  }
}
