import { Controller, Get, Inject, Param, Query } from '@midwayjs/core';
import { ServiceTypeService } from '../../service/service-type.service';
import { ServiceService } from '../../service/service.service';

@Controller('/openapi/service')
export class ServiceController {
  @Inject()
  service: ServiceTypeService;

  @Inject()
  serviceService: ServiceService;

  @Get('/types', { summary: '查询类型列表' })
  async index(@Query('type') type: string) {
    return this.service.findAll({ query: { type } });
  }

  @Get('/services/:typeId', { summary: '获取指定类目下的服务列表' })
  async getServicesByCategory(
    @Param('typeId') typeId: number,
    @Query('type') type: string,
    @Query('hairType') hairType: string,
    @Query('weightType') weightType: string
  ) {
    const services = await this.service.getServicesByType(typeId, {
      type,
      hairType,
      weightType,
    });
    return services;
  }

  @Get('/additional-service/:serviceId', { summary: '获取附加服务' })
  async getAdditionalService(@Param('serviceId') serviceId: number) {
    const res = await this.serviceService.getAdditionalService(serviceId);
    return res;
  }
}
