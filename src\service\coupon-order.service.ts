import { Provide, Inject } from '@midwayjs/core';
import { BaseService } from '../common/BaseService';
import { CouponOrder, CouponOrderStatus } from '../entity/coupon-order.entity';
import { CustomerCouponService } from './customer-coupon.service';
import { Coupon } from '../entity/coupon.entity';
import { Customer } from '../entity/customer.entity';
import { CustomError } from '../error/custom.error';
import { Op } from 'sequelize';

@Provide()
export class CouponOrderService extends BaseService<CouponOrder> {
  @Inject()
  customerCouponService: CustomerCouponService;

  constructor() {
    super('代金券订单');
  }

  getModel() {
    return CouponOrder;
  }

  /**
   * 创建代金券订单
   * @param customerId 用户ID
   * @param couponId 代金券ID
   * @param remark 备注
   */
  async createOrder(customerId: number, couponId: number, remark?: string) {
    // 查询用户信息
    const customer = await Customer.findByPk(customerId);
    if (!customer) {
      throw new CustomError('用户不存在');
    }

    // 查询代金券信息
    const coupon = await Coupon.findByPk(couponId);
    if (!coupon) {
      throw new CustomError('代金券不存在');
    }

    // 检查代金券是否启用
    if (!coupon.isEnabled) {
      throw new CustomError('该代金券已停用');
    }

    // 生成订单编号：CP + 当前时间戳 + 4位随机数
    const timestamp = Date.now().toString();
    const random = Math.floor(Math.random() * 10000)
      .toString()
      .padStart(4, '0');
    const sn = `CP${timestamp}${random}`;

    // 创建订单
    const order = await this.create({
      sn,
      customerId,
      couponId,
      amount: coupon.price,
      status: CouponOrderStatus.PENDING_PAYMENT,
      remark,
    });

    return order;
  }

  /**
   * 支付代金券订单
   * @param customerId 用户ID
   * @param sn 订单编号
   */
  async payOrder(customerId: number, sn: string) {
    // 查询订单信息
    const order = await this.findOne({ where: { sn } });
    if (!order) {
      throw new CustomError('订单不存在');
    }

    // 检查订单是否属于该用户
    if (order.customerId !== customerId) {
      throw new CustomError('订单不属于该用户');
    }

    // 检查订单状态
    if (order.status !== CouponOrderStatus.PENDING_PAYMENT) {
      throw new CustomError('订单状态不正确');
    }

    // 查询代金券信息
    const coupon = await Coupon.findByPk(order.couponId);
    if (!coupon) {
      throw new CustomError('代金券不存在');
    }

    // 更新订单状态
    await order.update({
      status: CouponOrderStatus.PAID,
      payTime: new Date(),
    });

    // 创建用户代金券
    const now = new Date();
    let expiryTime = null;
    if (coupon.validDays) {
      expiryTime = new Date(
        now.getTime() + coupon.validDays * 24 * 60 * 60 * 1000
      );
    }

    // 设置剩余使用次数
    let remainTimes = 1; // 默认一次使用
    if (coupon.usageLimit !== null && coupon.usageLimit !== undefined) {
      remainTimes = coupon.usageLimit;
    }

    const customerCoupon = await this.customerCouponService.createCoupon({
      customerId,
      couponId: order.couponId,
      receiveTime: now,
      expiryTime,
      remainTimes,
      status: 'active',
    });

    return {
      order,
      customerCoupon,
    };
  }

  /**
   * 取消代金券订单
   * @param customerId 用户ID
   * @param sn 订单编号
   */
  async cancelOrder(customerId: number, sn: string) {
    // 查询订单信息
    const order = await this.findOne({ where: { sn } });
    if (!order) {
      throw new CustomError('订单不存在');
    }

    // 检查订单是否属于该用户
    if (order.customerId !== customerId) {
      throw new CustomError('订单不属于该用户');
    }

    // 检查订单状态
    if (order.status !== CouponOrderStatus.PENDING_PAYMENT) {
      throw new CustomError('订单状态不正确');
    }

    // 更新订单状态
    await order.update({
      status: CouponOrderStatus.CANCELLED,
      cancelTime: new Date(),
    });

    return order;
  }

  /**
   * 查询用户的代金券订单列表
   * @param customerId 用户ID
   * @param status 订单状态
   * @param page 页码
   * @param pageSize 每页数量
   */
  async findCustomerOrders(
    customerId: number,
    status?: CouponOrderStatus[],
    page = 1,
    pageSize = 10
  ) {
    const query: any = { customerId };
    if (status && status.length > 0) {
      query.status = { [Op.in]: status };
    }

    return await this.findAll({
      query,
      offset: (page - 1) * pageSize,
      limit: pageSize,
      order: [['createdAt', 'DESC']],
      include: ['coupon'],
    });
  }
}
