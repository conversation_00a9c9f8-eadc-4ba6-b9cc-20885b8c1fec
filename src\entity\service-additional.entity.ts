import {
  Table,
  Column,
  Model,
  DataType,
  ForeignKey,
} from 'sequelize-typescript';
import { Service } from './service.entity';
import { AdditionalService } from './additional-service.entity';

@Table({
  tableName: 'service_additional',
  timestamps: true,
  comment: '服务-增项服务关联表',
})
export class ServiceAdditional extends Model {
  @ForeignKey(() => Service)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    unique: {
      name: 'unique_service_additional',
      msg: '已存在相同服务-增项服务关联',
    },
    comment: '服务ID',
  })
  sId: number;

  @ForeignKey(() => AdditionalService)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    unique: {
      name: 'unique_service_additional',
      msg: '已存在相同服务-增项服务关联',
    },
    comment: '增项服务ID',
  })
  aId: number;
}
