import { Provide } from '@midwayjs/core';
import { Role } from '../entity/role.entity';
import { BaseService } from '../common/BaseService';
import { Permission } from '../entity/permission.entity';
import { User } from '../entity/user.entity';
import { FindOptions } from 'sequelize';

@Provide()
export class RoleService extends BaseService<Role> {
  constructor() {
    super('角色');
  }

  getModel() {
    return Role;
  }

  async addPermissions(roleId: number, permissionIds: number[]) {
    const role = await this.findById(roleId);
    if (!role) {
      throw new Error('角色不存在');
    }
    const permissions = await Permission.findAll({
      where: { id: permissionIds },
    });
    await role.$add('permissions', permissions);
  }

  async delPermissions(roleId: number, permissionIds: number[]) {
    const role = await this.findById(roleId);
    if (!role) {
      throw new Error('角色不存在');
    }
    const permissions = await Permission.findAll({
      where: { id: permissionIds },
    });
    await role.$remove('permissions', permissions);
  }

  async getUsers(roleId: number, options: FindOptions) {
    const role = await Role.findOne({
      where: { id: roleId },
    });
    if (!role) {
      throw new Error('角色不存在');
    }
    const total = await role.$count('users');
    const users = await role.$get('users', options);
    return {
      total,
      list: users,
    };
  }

  async addUsers(roleId: number, userIds: number[]) {
    const role = await this.findById(roleId);
    if (!role) {
      throw new Error('角色不存在');
    }
    const users = await User.findAll({
      where: { id: userIds },
    });
    await role.$add('users', users);
  }

  async delUsers(roleId: number, userIds: number[]) {
    const role = await this.findById(roleId);
    if (!role) {
      throw new Error('角色不存在');
    }
    const users = await User.findAll({
      where: { id: userIds },
    });
    await role.$remove('users', users);
  }
}
