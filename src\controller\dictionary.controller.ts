import {
  Body,
  Controller,
  Del,
  Get,
  Inject,
  Param,
  Post,
  Put,
  Query,
} from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { DictionaryService } from '../service/dictionary.service';
import { CustomError } from '../error/custom.error';
import { Op } from 'sequelize';
import { DictionaryAttributes } from '../entity';

@Controller('/dictionaries')
export class DictionaryController {
  @Inject()
  ctx: Context;

  @Inject()
  service: DictionaryService;

  @Get('/', { summary: '查询字典列表' })
  async index(@Query() query: any) {
    let { offset, limit } = query;
    const { offset: o, limit: l, current, pageSize, ...queryInfo } = query;
    void o;
    void l;

    if (current && pageSize) {
      offset = (current - 1) * pageSize;
      limit = pageSize;
    }
    // name支持模糊查询
    if (queryInfo.name) {
      queryInfo.name = {
        [Op.like]: `%${queryInfo.name}%`,
      };
    }
    return this.service.findAll({
      query: queryInfo,
      offset,
      limit,
      order: [
        ['type', 'asc'],
        ['sortOrder', 'ASC'],
      ],
    });
  }

  @Get('/:id', { summary: '按ID查询字典' })
  async show(@Param('id') id: number) {
    const res = await this.service.findById(id);
    if (!res) {
      throw new CustomError('未找到指定字典');
    }
    return res;
  }

  @Post('/', { summary: '新增字典' })
  async create(@Body() info: DictionaryAttributes) {
    const sortOrder = await this.service.getNextOrder(info.type);
    const res = await this.service.create({ ...info, sortOrder });
    return res;
  }

  @Put('/:id', { summary: '更新字典' })
  async update(@Param('id') id: number, @Body() body: any) {
    await this.service.update({ id }, body);
    return true;
  }

  @Del('/:id', { summary: '删除字典' })
  async destroy(@Param('id') id: number) {
    await this.service.delete({ id });
    return true;
  }
}
