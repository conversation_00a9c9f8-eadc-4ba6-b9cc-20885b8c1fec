import {
  Body,
  Controller,
  Del,
  Get,
  Inject,
  Param,
  Post,
  Put,
  Query,
} from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { CouponService } from '../service/coupon.service';
import { CustomError } from '../error/custom.error';
import { WhereOptions } from 'sequelize';
import { QueryCouponDto } from '../dto/coupon.dto';
import { CouponAttributes } from '../entity';

@Controller('/coupons')
export class CouponController {
  @Inject()
  ctx: Context;

  @Inject()
  service: CouponService;

  @Get('/', { summary: '查询代金券列表' })
  async index(@Query() query: QueryCouponDto) {
    let { offset, limit } = query;
    const { offset: o, limit: l, current, pageSize, ...queryInfo } = query;
    void o;
    void l;

    const queryOpt: WhereOptions<CouponAttributes> = queryInfo;

    if (current && pageSize) {
      offset = (current - 1) * pageSize;
      limit = pageSize;
    }
    return this.service.findAll({
      query: queryOpt,
      offset,
      limit,
      order: [['createdAt', 'ASC']],
    });
  }

  @Get('/:id', { summary: '按ID查询代金券' })
  async show(@Param('id') id: number) {
    const res = await this.service.findById(id);
    if (!res) {
      throw new CustomError('未找到指定代金券');
    }
    return res;
  }

  @Post('/', { summary: '新增代金券' })
  async create(@Body() info: any) {
    const res = await this.service.create(info);
    return res;
  }

  @Put('/:id', { summary: '更新代金券' })
  async update(@Param('id') id: number, @Body() body: any) {
    await this.service.update({ id }, body);
    return true;
  }

  @Del('/:id', { summary: '删除代金券' })
  async destroy(@Param('id') id: number) {
    await this.service.delete({ id });
    return true;
  }
}
