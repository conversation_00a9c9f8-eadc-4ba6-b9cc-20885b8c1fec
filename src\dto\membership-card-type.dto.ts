import { Rule, RuleType } from '@midwayjs/validate';
import { BaseQueryDTO } from './BaseDTO';
import { ApplicableScope } from '../common/Constant';

/**
 * 权益卡类型查询DTO
 */
export class QueryMembershipCardTypeDto extends BaseQueryDTO {
  @Rule(RuleType.string().optional().error(new Error('名称必须是字符串')))
  name?: string;

  @Rule(RuleType.number().optional().error(new Error('售价必须是数字')))
  price?: number;

  @Rule(
    RuleType.string()
      .optional()
      .valid('discount', 'times')
      .error(new Error('权益卡类型必须是折扣卡(discount)或次卡(times)'))
  )
  type?: 'discount' | 'times';

  @Rule(RuleType.number().optional().error(new Error('有效期天数必须是数字')))
  validDays?: number;

  @Rule(RuleType.number().optional().error(new Error('折扣率必须是数字')))
  discountRate?: number;

  @Rule(RuleType.number().optional().error(new Error('可用次数必须是数字')))
  usageLimit?: number;

  @Rule(
    RuleType.string().optional().error(new Error('适用范围必须是有效的枚举值'))
  )
  applicableScope?: ApplicableScope;

  @Rule(RuleType.boolean().optional().error(new Error('是否启用必须是布尔值')))
  isEnabled?: boolean;
}

/**
 * 创建权益卡类型DTO
 */
export class CreateMembershipCardTypeDto {
  @Rule(
    RuleType.string()
      .required()
      .max(50)
      .error(new Error('权益卡名称不能为空且不能超过50个字符'))
  )
  /** 名称 */
  name: string;

  @Rule(
    RuleType.number()
      .required()
      .min(0)
      .error(new Error('售价不能为空且不能小于0'))
  )
  /** 售价 */
  price: number;

  @Rule(
    RuleType.string()
      .required()
      .valid('discount', 'times')
      .error(new Error('权益卡类型不能为空，必须是折扣卡(discount)或次卡(times)'))
  )
  /** 权益卡类型，折扣卡或次卡 */
  type: 'discount' | 'times';

  @Rule(
    RuleType.number()
      .optional()
      .min(1)
      .error(new Error('有效期天数必须大于0'))
  )
  /** 有效期天数，null表示无有效期限制 */
  validDays?: number;

  @Rule(
    RuleType.number()
      .optional()
      .min(0)
      .max(1)
      .error(new Error('折扣率必须在0到1之间'))
  )
  /** 折扣率 */
  discountRate?: number;

  @Rule(
    RuleType.number()
      .optional()
      .min(-1)
      .error(new Error('可用次数必须大于等于-1，-1表示不限次数'))
  )
  /** 可用次数，-1表示不限次数 */
  usageLimit?: number;

  @Rule(RuleType.string().required().error(new Error('适用范围不能为空')))
  /** 适用范围 */
  applicableScope: ApplicableScope;

  @Rule(
    RuleType.array()
      .items(RuleType.string())
      .optional()
      .error(new Error('适用服务类型必须是字符串数组'))
  )
  /** 适用服务类型名称列表，适用范围为指定服务类型时必填 */
  applicableServiceTypes?: string[];

  @Rule(
    RuleType.array()
      .items(RuleType.number())
      .optional()
      .error(new Error('适用服务品牌ID必须是数字数组'))
  )
  /** 适用服务品牌ID列表，适用范围为指定服务品牌时必填 */
  applicableServiceCategories?: number[];

  @Rule(
    RuleType.array()
      .items(RuleType.number())
      .optional()
      .error(new Error('适用服务ID必须是数字数组'))
  )
  /** 适用服务ID列表，适用范围为指定服务时必填 */
  applicableServices?: number[];

  @Rule(
    RuleType.array()
      .items(RuleType.number())
      .optional()
      .error(new Error('适用商品类别ID必须是数字数组'))
  )
  /** 适用商品类别ID列表，适用范围为指定商品类别时必填 */
  applicableProductTypes?: number[];

  @Rule(
    RuleType.array()
      .items(RuleType.number())
      .optional()
      .error(new Error('适用商品ID必须是数字数组'))
  )
  /** 适用商品ID列表，适用范围为指定商品时必填 */
  applicableProducts?: number[];

  @Rule(RuleType.string().optional().error(new Error('权益描述必须是字符串')))
  /** 权益描述 */
  description?: string;

  @Rule(
    RuleType.boolean().default(true).error(new Error('是否启用必须是布尔值'))
  )
  /** 是否启用 */
  isEnabled: boolean;
}

/**
 * 更新权益卡类型DTO
 */
export class UpdateMembershipCardTypeDto {
  @Rule(
    RuleType.string()
      .optional()
      .max(50)
      .error(new Error('权益卡名称不能超过50个字符'))
  )
  /** 名称 */
  name?: string;

  @Rule(RuleType.number().optional().min(0).error(new Error('售价不能小于0')))
  /** 售价 */
  price?: number;

  @Rule(
    RuleType.string()
      .optional()
      .valid('discount', 'times')
      .error(new Error('权益卡类型必须是折扣卡(discount)或次卡(times)'))
  )
  /** 权益卡类型，折扣卡或次卡 */
  type?: 'discount' | 'times';

  @Rule(
    RuleType.number().optional().min(1).error(new Error('有效期天数必须大于0'))
  )
  /** 有效期天数 */
  validDays?: number;

  @Rule(
    RuleType.number()
      .optional()
      .min(0)
      .max(1)
      .error(new Error('折扣率必须在0到1之间'))
  )
  /** 折扣率 */
  discountRate?: number;

  @Rule(
    RuleType.number()
      .optional()
      .min(-1)
      .error(new Error('可用次数必须大于等于-1，-1表示不限次数'))
  )
  /** 可用次数，-1表示不限次数 */
  usageLimit?: number;

  @Rule(
    RuleType.string().optional().error(new Error('适用范围必须是有效的枚举值'))
  )
  /** 适用范围 */
  applicableScope?: ApplicableScope;

  @Rule(
    RuleType.array()
      .items(RuleType.string())
      .optional()
      .error(new Error('适用服务类型必须是字符串数组'))
  )
  /** 适用服务类型名称列表，适用范围为指定服务类型时必填 */
  applicableServiceTypes?: string[];

  @Rule(
    RuleType.array()
      .items(RuleType.number())
      .optional()
      .error(new Error('适用服务品牌ID必须是数字数组'))
  )
  /** 适用服务品牌ID列表，适用范围为指定服务品牌时必填 */
  applicableServiceCategories?: number[];

  @Rule(
    RuleType.array()
      .items(RuleType.number())
      .optional()
      .error(new Error('适用服务ID必须是数字数组'))
  )
  /** 适用服务ID列表，适用范围为指定服务时必填 */
  applicableServices?: number[];

  @Rule(
    RuleType.array()
      .items(RuleType.number())
      .optional()
      .error(new Error('适用商品类别ID必须是数字数组'))
  )
  /** 适用商品类别ID列表，适用范围为指定商品类别时必填 */
  applicableProductTypes?: number[];

  @Rule(
    RuleType.array()
      .items(RuleType.number())
      .optional()
      .error(new Error('适用商品ID必须是数字数组'))
  )
  /** 适用商品ID列表，适用范围为指定商品时必填 */
  applicableProducts?: number[];

  @Rule(RuleType.string().optional().error(new Error('权益描述必须是字符串')))
  /** 权益描述 */
  description?: string;

  @Rule(RuleType.boolean().optional().error(new Error('是否启用必须是布尔值')))
  /** 是否启用 */
  isEnabled?: boolean;
}
