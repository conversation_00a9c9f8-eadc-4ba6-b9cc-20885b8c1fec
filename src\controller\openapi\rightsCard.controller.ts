/*
 * @Description: 权益卡相关接口，不用认证
 * @Date: 2025-05-12 22:30:59
 * @LastEditors: Zhu<PERSON><PERSON>liang <EMAIL>
 * @LastEditTime: 2025-05-13 07:08:43
 */
import { Controller, Get, Inject } from '@midwayjs/core';
import { MembershipCardTypeService } from '../../service/membership-card-type.service';

@Controller('/openapi/rights-card')
export class RightsCardController {
  @Inject()
  service: MembershipCardTypeService;

  @Get('/', { summary: '查询权限卡类列表' })
  async index() {
    return this.service.findAll({ query: { isEnabled: true } });
  }
}
