import {
  Table,
  Column,
  Model,
  DataType,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { Customer } from './customer.entity';

export interface PromotionRecordAttributes {
  /** 记录ID */
  id: number;
  /** 分享人微信openid */
  sharerOpenid: string;
  /** 分享人系统用户ID */
  sharerUserId: number;
  /** 分享时间 */
  shareTime: Date;
  /** 注册人微信openid */
  registrantOpenid: string;
  /** 注册人系统用户ID */
  registrantUserId: number;
  /** 注册时间 */
  registerTime?: Date;
  /** 分享码 */
  promotionCode: string;
  /** 状态：有效、已使用、过期 */
  status: string;
}

@Table({
  tableName: 'promotion_records',
  timestamps: true,
  indexes: [
    {
      name: 'registrant_openid_unique',
      fields: ['registrantOpenid'],
      unique: true,
    },
    {
      name: 'sharer_registrant_openid_unique',
      fields: ['sharerOpenid', 'registrantOpenid'],
      unique: true,
    },
  ],
  comment: '推广记录表',
})
export class PromotionRecord
  extends Model<PromotionRecordAttributes>
  implements PromotionRecordAttributes
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '记录ID',
  })
  id: number;

  @Column({
    type: DataType.STRING(50),
    allowNull: false,
    comment: '分享人微信openid',
  })
  sharerOpenid: string;

  @ForeignKey(() => Customer)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '分享人系统用户ID',
  })
  sharerUserId: number;

  @BelongsTo(() => Customer, {
    as: 'sharer',
    targetKey: 'id',
    foreignKey: 'sharerUserId',
    onDelete: 'CASCADE',
  })
  sharer: Customer;

  @Column({
    type: DataType.DATE,
    allowNull: false,
    comment: '分享时间',
  })
  shareTime: Date;

  @Column({
    type: DataType.STRING(50),
    allowNull: false,
    comment: '注册人微信openid',
  })
  registrantOpenid: string;

  @ForeignKey(() => Customer)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '注册人系统用户ID',
  })
  registrantUserId: number;

  @BelongsTo(() => Customer, {
    as: 'registrant',
    targetKey: 'id',
    foreignKey: 'registrantUserId',
    onDelete: 'CASCADE',
  })
  registrant?: Customer;

  @Column({
    type: DataType.DATE,
    comment: '注册时间',
  })
  registerTime?: Date;

  @Column({
    type: DataType.STRING(20),
    allowNull: false,
    comment: '分享码',
  })
  promotionCode: string;

  @Column({
    type: DataType.STRING(20),
    defaultValue: '有效',
    comment: '状态：有效、已使用、过期',
  })
  status: string;
}
